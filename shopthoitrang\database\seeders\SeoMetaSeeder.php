<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SeoMetaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // SEO Meta data
        DB::table('seo_meta')->insert([
            [
                'url' => '/',
                'title' => 'Shop Thời Trang - Thời trang trực tuyến hàng đầu Việt Nam',
                'description' => 'Khám phá bộ sưu tập thời trang đa dạng tại Shop Thời Trang. Chất lượng cao, gi<PERSON> cả hợp lý, giao hàng toàn quốc, đổi trả dễ dàng. Mua sắm ngay!',
                'keywords' => 'shop thời trang, quần áo online, thời trang nam, thời trang nữ, mua sắm trực tuyến, thời trang việt nam',
                'canonical_url' => 'https://shopthoitrang.com/',
                'og_tags' => json_encode([
                    'og:title' => 'Shop Thời Trang - Thời trang trực tuyến hàng đầu',
                    'og:description' => 'Khám phá bộ sưu tập thời trang đa dạng với chất lượng cao và giá cả hợp lý',
                    'og:image' => 'https://shopthoitrang.com/images/og-home.jpg',
                    'og:url' => 'https://shopthoitrang.com/',
                    'og:type' => 'website'
                ]),
                'twitter_tags' => json_encode([
                    'twitter:card' => 'summary_large_image',
                    'twitter:title' => 'Shop Thời Trang - Thời trang trực tuyến hàng đầu',
                    'twitter:description' => 'Khám phá bộ sưu tập thời trang đa dạng',
                    'twitter:image' => 'https://shopthoitrang.com/images/twitter-home.jpg'
                ]),
                'schema_markup' => json_encode([
                    '@context' => 'https://schema.org',
                    '@type' => 'Organization',
                    'name' => 'Shop Thời Trang',
                    'url' => 'https://shopthoitrang.com',
                    'logo' => 'https://shopthoitrang.com/images/logo.png',
                    'contactPoint' => [
                        '@type' => 'ContactPoint',
                        'telephone' => '1900-1234',
                        'contactType' => 'customer service'
                    ]
                ]),
                'robots' => 'index,follow',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'url' => '/products',
                'title' => 'Sản phẩm - Shop Thời Trang',
                'description' => 'Tìm kiếm và mua sắm hàng ngàn sản phẩm thời trang chất lượng cao tại Shop Thời Trang. Từ áo quần đến phụ kiện, tất cả đều có tại đây.',
                'keywords' => 'sản phẩm thời trang, áo quần, phụ kiện, giày dép, túi xách',
                'canonical_url' => 'https://shopthoitrang.com/products',
                'og_tags' => null,
                'twitter_tags' => null,
                'schema_markup' => null,
                'robots' => 'index,follow',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'url' => '/categories/thoi-trang-nam',
                'title' => 'Thời trang Nam - Shop Thời Trang',
                'description' => 'Bộ sưu tập thời trang nam đa dạng với áo sơ mi, quần tây, áo thun và nhiều sản phẩm khác. Phong cách lịch lãm cho phái mạnh.',
                'keywords' => 'thời trang nam, áo nam, quần nam, phụ kiện nam, thời trang công sở nam',
                'canonical_url' => 'https://shopthoitrang.com/categories/thoi-trang-nam',
                'og_tags' => null,
                'twitter_tags' => null,
                'schema_markup' => null,
                'robots' => 'index,follow',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'url' => '/categories/thoi-trang-nu',
                'title' => 'Thời trang Nữ - Shop Thời Trang',
                'description' => 'Bộ sưu tập thời trang nữ với đầm, áo, quần và phụ kiện thời trang cao cấp. Tôn vinh vẻ đẹp phái nữ.',
                'keywords' => 'thời trang nữ, đầm nữ, áo nữ, quần nữ, phụ kiện nữ',
                'canonical_url' => 'https://shopthoitrang.com/categories/thoi-trang-nu',
                'og_tags' => null,
                'twitter_tags' => null,
                'schema_markup' => null,
                'robots' => 'index,follow',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'url' => '/blog',
                'title' => 'Blog thời trang - Xu hướng và mẹo phối đồ',
                'description' => 'Cập nhật xu hướng thời trang mới nhất, mẹo phối đồ và cách chăm sóc quần áo. Blog thời trang hàng đầu Việt Nam.',
                'keywords' => 'blog thời trang, xu hướng thời trang, mẹo phối đồ, chăm sóc quần áo',
                'canonical_url' => 'https://shopthoitrang.com/blog',
                'og_tags' => null,
                'twitter_tags' => null,
                'schema_markup' => null,
                'robots' => 'index,follow',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ]);

        // URL Redirects
        DB::table('redirects')->insert([
            [
                'from_url' => '/old-products',
                'to_url' => '/products',
                'status_code' => 301,
                'is_active' => true,
                'hits_count' => 0,
                'description' => 'Redirect old products page to new products page',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'from_url' => '/men-fashion',
                'to_url' => '/categories/thoi-trang-nam',
                'status_code' => 301,
                'is_active' => true,
                'hits_count' => 0,
                'description' => 'Redirect old men fashion URL to new category URL',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'from_url' => '/women-fashion',
                'to_url' => '/categories/thoi-trang-nu',
                'status_code' => 301,
                'is_active' => true,
                'hits_count' => 0,
                'description' => 'Redirect old women fashion URL to new category URL',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ]);

        // Sitemap entries
        DB::table('sitemap_entries')->insert([
            [
                'url' => 'https://shopthoitrang.com/',
                'changefreq' => 'daily',
                'priority' => 1.0,
                'lastmod' => Carbon::now(),
                'is_active' => true,
                'type' => 'page',
                'entity_id' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'url' => 'https://shopthoitrang.com/products',
                'changefreq' => 'hourly',
                'priority' => 0.9,
                'lastmod' => Carbon::now(),
                'is_active' => true,
                'type' => 'page',
                'entity_id' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'url' => 'https://shopthoitrang.com/categories/thoi-trang-nam',
                'changefreq' => 'daily',
                'priority' => 0.8,
                'lastmod' => Carbon::now(),
                'is_active' => true,
                'type' => 'category',
                'entity_id' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'url' => 'https://shopthoitrang.com/categories/thoi-trang-nu',
                'changefreq' => 'daily',
                'priority' => 0.8,
                'lastmod' => Carbon::now(),
                'is_active' => true,
                'type' => 'category',
                'entity_id' => 2,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'url' => 'https://shopthoitrang.com/categories/phu-kien',
                'changefreq' => 'daily',
                'priority' => 0.8,
                'lastmod' => Carbon::now(),
                'is_active' => true,
                'type' => 'category',
                'entity_id' => 3,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'url' => 'https://shopthoitrang.com/blog',
                'changefreq' => 'weekly',
                'priority' => 0.7,
                'lastmod' => Carbon::now(),
                'is_active' => true,
                'type' => 'page',
                'entity_id' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'url' => 'https://shopthoitrang.com/ve-chung-toi',
                'changefreq' => 'monthly',
                'priority' => 0.5,
                'lastmod' => Carbon::now(),
                'is_active' => true,
                'type' => 'page',
                'entity_id' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'url' => 'https://shopthoitrang.com/lien-he',
                'changefreq' => 'monthly',
                'priority' => 0.6,
                'lastmod' => Carbon::now(),
                'is_active' => true,
                'type' => 'page',
                'entity_id' => 5,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ]);
    }
}
