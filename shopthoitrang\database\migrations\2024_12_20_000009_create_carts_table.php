<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('carts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('new_users')->onDelete('cascade');
            $table->string('session_id')->nullable(); // For guest users
            $table->foreignId('product_id')->constrained('new_products')->onDelete('cascade');
            $table->foreignId('product_variant_id')->nullable()->constrained('product_variants')->onDelete('cascade');
            
            $table->integer('quantity');
            $table->decimal('unit_price', 12, 2); // Price at time of adding to cart
            
            // Custom options for the product
            $table->json('custom_options')->nullable(); // Personalization, gift wrapping, etc.
            
            // Cart item status
            $table->boolean('is_saved_for_later')->default(false);
            
            $table->timestamps();
            
            // Indexes
            $table->index(['user_id', 'created_at']);
            $table->index(['session_id', 'created_at']);
            $table->index(['product_id', 'product_variant_id']);
            
            // Unique constraint to prevent duplicate items
            $table->unique(['user_id', 'product_id', 'product_variant_id', 'session_id'], 'unique_cart_item');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('carts');
    }
};
