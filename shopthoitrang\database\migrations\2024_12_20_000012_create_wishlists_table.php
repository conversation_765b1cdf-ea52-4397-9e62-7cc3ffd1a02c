<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wishlists', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('new_users')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('new_products')->onDelete('cascade');
            $table->foreignId('product_variant_id')->nullable()->constrained('product_variants')->onDelete('cascade');
            
            // Wishlist organization
            $table->string('list_name')->default('default'); // Users can have multiple wishlists
            $table->text('notes')->nullable(); // Personal notes about the product
            $table->integer('priority')->default(0); // Priority within the wishlist
            
            // Notification settings
            $table->boolean('notify_price_drop')->default(true);
            $table->boolean('notify_back_in_stock')->default(true);
            $table->decimal('target_price', 12, 2)->nullable(); // Notify when price drops to this level
            
            $table->timestamps();
            
            // Indexes
            $table->index(['user_id', 'list_name', 'created_at']);
            $table->index(['product_id', 'created_at']);
            
            // Unique constraint
            $table->unique(['user_id', 'product_id', 'product_variant_id', 'list_name'], 'unique_wishlist_item');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wishlists');
    }
};
