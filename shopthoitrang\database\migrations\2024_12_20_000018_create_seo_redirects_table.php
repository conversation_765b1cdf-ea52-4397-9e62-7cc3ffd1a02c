<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // SEO Meta table for dynamic SEO management
        Schema::create('seo_meta', function (Blueprint $table) {
            $table->id();
            $table->string('url')->unique();
            $table->string('title')->nullable();
            $table->text('description')->nullable();
            $table->text('keywords')->nullable();
            $table->string('canonical_url')->nullable();
            $table->json('og_tags')->nullable(); // Open Graph tags
            $table->json('twitter_tags')->nullable(); // Twitter Card tags
            $table->json('schema_markup')->nullable(); // JSON-LD structured data
            $table->string('robots')->default('index,follow'); // Robot directives
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['url', 'is_active']);
        });
        
        // URL Redirects for SEO
        Schema::create('redirects', function (Blueprint $table) {
            $table->id();
            $table->string('from_url');
            $table->string('to_url');
            $table->integer('status_code')->default(301); // 301, 302, etc.
            $table->boolean('is_active')->default(true);
            $table->integer('hits_count')->default(0); // Track redirect usage
            $table->text('description')->nullable();
            $table->timestamps();
            
            $table->index(['from_url', 'is_active']);
            $table->index('to_url');
        });
        
        // Sitemap entries
        Schema::create('sitemap_entries', function (Blueprint $table) {
            $table->id();
            $table->string('url');
            $table->enum('changefreq', ['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never'])->default('weekly');
            $table->decimal('priority', 2, 1)->default(0.5); // 0.0 to 1.0
            $table->timestamp('lastmod')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('type')->nullable(); // product, category, page, blog, etc.
            $table->unsignedBigInteger('entity_id')->nullable(); // ID of the related entity
            $table->timestamps();
            
            $table->index(['url', 'is_active']);
            $table->index(['type', 'entity_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sitemap_entries');
        Schema::dropIfExists('redirects');
        Schema::dropIfExists('seo_meta');
    }
};
