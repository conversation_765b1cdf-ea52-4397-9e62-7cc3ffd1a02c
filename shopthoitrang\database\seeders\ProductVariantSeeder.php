<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ProductVariantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Variants cho Áo sơ mi nam (product_id = 1)
        $shirtVariants = [
            // Size S
            ['size' => 'S', 'color' => 'Trắng', 'sku_suffix' => 'S-WHITE', 'quantity' => 20],
            ['size' => 'S', 'color' => 'Xanh nhạt', 'sku_suffix' => 'S-LIGHTBLUE', 'quantity' => 15],
            ['size' => 'S', 'color' => 'Hồng nhạt', 'sku_suffix' => 'S-LIGHTPINK', 'quantity' => 10],
            
            // Size M
            ['size' => 'M', 'color' => 'Trắng', 'sku_suffix' => 'M-WHITE', 'quantity' => 25, 'is_default' => true],
            ['size' => 'M', 'color' => 'Xanh nhạt', 'sku_suffix' => 'M-LIGHTBLUE', 'quantity' => 20],
            ['size' => 'M', 'color' => 'Hồng nhạt', 'sku_suffix' => 'M-LIGHTPINK', 'quantity' => 15],
            
            // Size L
            ['size' => 'L', 'color' => 'Trắng', 'sku_suffix' => 'L-WHITE', 'quantity' => 20],
            ['size' => 'L', 'color' => 'Xanh nhạt', 'sku_suffix' => 'L-LIGHTBLUE', 'quantity' => 15],
            ['size' => 'L', 'color' => 'Hồng nhạt', 'sku_suffix' => 'L-LIGHTPINK', 'quantity' => 10],
            
            // Size XL
            ['size' => 'XL', 'color' => 'Trắng', 'sku_suffix' => 'XL-WHITE', 'quantity' => 15],
            ['size' => 'XL', 'color' => 'Xanh nhạt', 'sku_suffix' => 'XL-LIGHTBLUE', 'quantity' => 10],
        ];

        foreach ($shirtVariants as $index => $variant) {
            DB::table('product_variants')->insert([
                'product_id' => 1,
                'title' => $variant['color'] . ' / ' . $variant['size'],
                'sku' => 'ASM-001-' . $variant['sku_suffix'],
                'price' => 299000,
                'compare_price' => 399000,
                'cost_price' => 150000,
                'quantity' => $variant['quantity'],
                'min_quantity' => 5,
                'track_quantity' => true,
                'weight' => 200,
                'option1_name' => 'Màu sắc',
                'option1_value' => $variant['color'],
                'option2_name' => 'Kích thước',
                'option2_value' => $variant['size'],
                'image' => 'products/ao-so-mi-nam-' . strtolower($variant['color']) . '.jpg',
                'is_active' => true,
                'is_default' => $variant['is_default'] ?? false,
                'position' => $index + 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }

        // Variants cho Đầm maxi (product_id = 2)
        $dressVariants = [
            // Size S
            ['size' => 'S', 'color' => 'Hồng', 'sku_suffix' => 'S-PINK', 'quantity' => 15],
            ['size' => 'S', 'color' => 'Xanh', 'sku_suffix' => 'S-BLUE', 'quantity' => 12],
            
            // Size M
            ['size' => 'M', 'color' => 'Hồng', 'sku_suffix' => 'M-PINK', 'quantity' => 20, 'is_default' => true],
            ['size' => 'M', 'color' => 'Xanh', 'sku_suffix' => 'M-BLUE', 'quantity' => 18],
            ['size' => 'M', 'color' => 'Vàng', 'sku_suffix' => 'M-YELLOW', 'quantity' => 15],
            
            // Size L
            ['size' => 'L', 'color' => 'Hồng', 'sku_suffix' => 'L-PINK', 'quantity' => 15],
            ['size' => 'L', 'color' => 'Xanh', 'sku_suffix' => 'L-BLUE', 'quantity' => 12],
            ['size' => 'L', 'color' => 'Vàng', 'sku_suffix' => 'L-YELLOW', 'quantity' => 10],
        ];

        foreach ($dressVariants as $index => $variant) {
            DB::table('product_variants')->insert([
                'product_id' => 2,
                'title' => $variant['color'] . ' / ' . $variant['size'],
                'sku' => 'DAM-001-' . $variant['sku_suffix'],
                'price' => 450000,
                'compare_price' => 550000,
                'cost_price' => 200000,
                'quantity' => $variant['quantity'],
                'min_quantity' => 3,
                'track_quantity' => true,
                'weight' => 300,
                'option1_name' => 'Màu sắc',
                'option1_value' => $variant['color'],
                'option2_name' => 'Kích thước',
                'option2_value' => $variant['size'],
                'image' => 'products/dam-maxi-' . strtolower($variant['color']) . '.jpg',
                'is_active' => true,
                'is_default' => $variant['is_default'] ?? false,
                'position' => $index + 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }

        // Variants cho Quần jean nam (product_id = 3)
        $jeansVariants = [
            // Size 29
            ['size' => '29', 'color' => 'Xanh đậm', 'sku_suffix' => '29-DARKBLUE', 'quantity' => 20],
            ['size' => '29', 'color' => 'Xanh nhạt', 'sku_suffix' => '29-LIGHTBLUE', 'quantity' => 15],
            
            // Size 30
            ['size' => '30', 'color' => 'Xanh đậm', 'sku_suffix' => '30-DARKBLUE', 'quantity' => 25, 'is_default' => true],
            ['size' => '30', 'color' => 'Xanh nhạt', 'sku_suffix' => '30-LIGHTBLUE', 'quantity' => 20],
            ['size' => '30', 'color' => 'Đen', 'sku_suffix' => '30-BLACK', 'quantity' => 18],
            
            // Size 31
            ['size' => '31', 'color' => 'Xanh đậm', 'sku_suffix' => '31-DARKBLUE', 'quantity' => 22],
            ['size' => '31', 'color' => 'Xanh nhạt', 'sku_suffix' => '31-LIGHTBLUE', 'quantity' => 18],
            ['size' => '31', 'color' => 'Đen', 'sku_suffix' => '31-BLACK', 'quantity' => 15],
            
            // Size 32
            ['size' => '32', 'color' => 'Xanh đậm', 'sku_suffix' => '32-DARKBLUE', 'quantity' => 20],
            ['size' => '32', 'color' => 'Đen', 'sku_suffix' => '32-BLACK', 'quantity' => 12],
        ];

        foreach ($jeansVariants as $index => $variant) {
            DB::table('product_variants')->insert([
                'product_id' => 3,
                'title' => $variant['color'] . ' / ' . $variant['size'],
                'sku' => 'QJ-001-' . $variant['sku_suffix'],
                'price' => 350000,
                'compare_price' => 450000,
                'cost_price' => 180000,
                'quantity' => $variant['quantity'],
                'min_quantity' => 5,
                'track_quantity' => true,
                'weight' => 500,
                'option1_name' => 'Màu sắc',
                'option1_value' => $variant['color'],
                'option2_name' => 'Kích thước',
                'option2_value' => $variant['size'],
                'image' => 'products/quan-jean-' . strtolower(str_replace(' ', '-', $variant['color'])) . '.jpg',
                'is_active' => true,
                'is_default' => $variant['is_default'] ?? false,
                'position' => $index + 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }

        // Variants cho Áo thun nữ (product_id = 4)
        $tshirtVariants = [
            // Size S
            ['size' => 'S', 'color' => 'Trắng', 'sku_suffix' => 'S-WHITE', 'quantity' => 30],
            ['size' => 'S', 'color' => 'Đen', 'sku_suffix' => 'S-BLACK', 'quantity' => 25],
            ['size' => 'S', 'color' => 'Hồng', 'sku_suffix' => 'S-PINK', 'quantity' => 20],
            
            // Size M
            ['size' => 'M', 'color' => 'Trắng', 'sku_suffix' => 'M-WHITE', 'quantity' => 35, 'is_default' => true],
            ['size' => 'M', 'color' => 'Đen', 'sku_suffix' => 'M-BLACK', 'quantity' => 30],
            ['size' => 'M', 'color' => 'Hồng', 'sku_suffix' => 'M-PINK', 'quantity' => 25],
            ['size' => 'M', 'color' => 'Xanh', 'sku_suffix' => 'M-BLUE', 'quantity' => 20],
            
            // Size L
            ['size' => 'L', 'color' => 'Trắng', 'sku_suffix' => 'L-WHITE', 'quantity' => 30],
            ['size' => 'L', 'color' => 'Đen', 'sku_suffix' => 'L-BLACK', 'quantity' => 25],
            ['size' => 'L', 'color' => 'Hồng', 'sku_suffix' => 'L-PINK', 'quantity' => 20],
            ['size' => 'L', 'color' => 'Xanh', 'sku_suffix' => 'L-BLUE', 'quantity' => 15],
        ];

        foreach ($tshirtVariants as $index => $variant) {
            DB::table('product_variants')->insert([
                'product_id' => 4,
                'title' => $variant['color'] . ' / ' . $variant['size'],
                'sku' => 'AT-001-' . $variant['sku_suffix'],
                'price' => 150000,
                'compare_price' => 200000,
                'cost_price' => 75000,
                'quantity' => $variant['quantity'],
                'min_quantity' => 10,
                'track_quantity' => true,
                'weight' => 150,
                'option1_name' => 'Màu sắc',
                'option1_value' => $variant['color'],
                'option2_name' => 'Kích thước',
                'option2_value' => $variant['size'],
                'image' => 'products/ao-thun-nu-' . strtolower($variant['color']) . '.jpg',
                'is_active' => true,
                'is_default' => $variant['is_default'] ?? false,
                'position' => $index + 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }

        // Variants cho Túi xách (product_id = 5) - chỉ có màu sắc
        $bagVariants = [
            ['color' => 'Đen', 'sku_suffix' => 'BLACK', 'quantity' => 20, 'is_default' => true],
            ['color' => 'Nâu', 'sku_suffix' => 'BROWN', 'quantity' => 15],
            ['color' => 'Đỏ', 'sku_suffix' => 'RED', 'quantity' => 10],
            ['color' => 'Xanh navy', 'sku_suffix' => 'NAVY', 'quantity' => 8],
        ];

        foreach ($bagVariants as $index => $variant) {
            DB::table('product_variants')->insert([
                'product_id' => 5,
                'title' => $variant['color'],
                'sku' => 'TX-001-' . $variant['sku_suffix'],
                'price' => 850000,
                'compare_price' => 1200000,
                'cost_price' => 400000,
                'quantity' => $variant['quantity'],
                'min_quantity' => 2,
                'track_quantity' => true,
                'weight' => 600,
                'option1_name' => 'Màu sắc',
                'option1_value' => $variant['color'],
                'image' => 'products/tui-xach-' . strtolower(str_replace(' ', '-', $variant['color'])) . '.jpg',
                'is_active' => true,
                'is_default' => $variant['is_default'] ?? false,
                'position' => $index + 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }
    }
}
