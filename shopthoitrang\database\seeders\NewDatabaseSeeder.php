<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class NewDatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            NewUserSeeder::class,
            BrandSeeder::class,
            NewCategorySeeder::class,
            TagSeeder::class,
            NewProductSeeder::class,
            ProductCategorySeeder::class,
            ProductTagSeeder::class,
            ProductVariantSeeder::class,
            CouponSeeder::class,
            ShippingZoneSeeder::class,
            PaymentMethodSeeder::class,
            PageSeeder::class,
            BlogSeeder::class,
            SettingSeeder::class,
            BannerSeeder::class,
            NewsletterSeeder::class,
            SeoMetaSeeder::class,
        ]);
    }
}
