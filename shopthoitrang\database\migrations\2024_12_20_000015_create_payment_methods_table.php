<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique(); // COD, VNPAY, MOMO, BANK_TRANSFER, etc.
            $table->text('description')->nullable();
            $table->string('icon')->nullable();
            
            // Payment gateway settings
            $table->string('gateway')->nullable(); // VNPay, MoMo, PayPal, Stripe, etc.
            $table->json('gateway_config')->nullable(); // API keys, endpoints, etc.
            
            // Availability
            $table->boolean('is_active')->default(true);
            $table->json('available_countries')->nullable(); // Country restrictions
            $table->decimal('min_amount', 12, 2)->nullable();
            $table->decimal('max_amount', 12, 2)->nullable();
            
            // Fees
            $table->decimal('fixed_fee', 12, 2)->default(0);
            $table->decimal('percentage_fee', 5, 4)->default(0); // As decimal (0.03 = 3%)
            
            // Display settings
            $table->integer('sort_order')->default(0);
            $table->text('instructions')->nullable(); // Instructions for customers
            
            // Features
            $table->boolean('supports_refunds')->default(false);
            $table->boolean('supports_partial_refunds')->default(false);
            $table->boolean('requires_confirmation')->default(false); // Manual confirmation needed
            
            $table->timestamps();
            
            $table->index(['is_active', 'sort_order']);
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};
