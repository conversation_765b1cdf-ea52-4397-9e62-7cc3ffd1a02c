<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'Shop Thời Trang',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Tên website',
                'description' => 'Tên hiển thị của website',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'site_description',
                'value' => 'Cửa hàng thời trang trực tuyến hàng đầu Việt Nam',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Mô tả website',
                'description' => 'Mô tả ngắn về website',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'site_logo',
                'value' => 'logo.png',
                'type' => 'file',
                'group' => 'general',
                'label' => 'Logo website',
                'description' => 'Logo chính của website',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Email liên hệ',
                'description' => 'Email chính để liên hệ',
                'is_public' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'contact_phone',
                'value' => '1900 1234',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Số điện thoại',
                'description' => 'Số điện thoại liên hệ',
                'is_public' => true,
                'sort_order' => 5,
            ],
            [
                'key' => 'contact_address',
                'value' => '123 Nguyễn Huệ, Quận 1, TP.HCM',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Địa chỉ',
                'description' => 'Địa chỉ cửa hàng',
                'is_public' => true,
                'sort_order' => 6,
            ],

            // SEO Settings
            [
                'key' => 'seo_title',
                'value' => 'Shop Thời Trang - Thời trang trực tuyến hàng đầu',
                'type' => 'string',
                'group' => 'seo',
                'label' => 'SEO Title',
                'description' => 'Tiêu đề SEO cho trang chủ',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'seo_description',
                'value' => 'Khám phá bộ sưu tập thời trang đa dạng với chất lượng cao và giá cả hợp lý. Giao hàng toàn quốc, đổi trả dễ dàng.',
                'type' => 'string',
                'group' => 'seo',
                'label' => 'SEO Description',
                'description' => 'Mô tả SEO cho trang chủ',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'seo_keywords',
                'value' => 'thời trang, quần áo, shop online, thời trang nam, thời trang nữ',
                'type' => 'string',
                'group' => 'seo',
                'label' => 'SEO Keywords',
                'description' => 'Từ khóa SEO cho trang chủ',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'google_analytics',
                'value' => '',
                'type' => 'string',
                'group' => 'seo',
                'label' => 'Google Analytics ID',
                'description' => 'Mã Google Analytics',
                'is_public' => false,
                'sort_order' => 4,
            ],
            [
                'key' => 'facebook_pixel',
                'value' => '',
                'type' => 'string',
                'group' => 'seo',
                'label' => 'Facebook Pixel ID',
                'description' => 'Mã Facebook Pixel',
                'is_public' => false,
                'sort_order' => 5,
            ],

            // E-commerce Settings
            [
                'key' => 'currency',
                'value' => 'VND',
                'type' => 'string',
                'group' => 'ecommerce',
                'label' => 'Đơn vị tiền tệ',
                'description' => 'Đơn vị tiền tệ mặc định',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'currency_symbol',
                'value' => '₫',
                'type' => 'string',
                'group' => 'ecommerce',
                'label' => 'Ký hiệu tiền tệ',
                'description' => 'Ký hiệu hiển thị cho tiền tệ',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'tax_rate',
                'value' => '10',
                'type' => 'integer',
                'group' => 'ecommerce',
                'label' => 'Thuế VAT (%)',
                'description' => 'Tỷ lệ thuế VAT',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'free_shipping_threshold',
                'value' => '500000',
                'type' => 'integer',
                'group' => 'ecommerce',
                'label' => 'Miễn phí ship từ',
                'description' => 'Giá trị đơn hàng để được miễn phí ship',
                'is_public' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'products_per_page',
                'value' => '12',
                'type' => 'integer',
                'group' => 'ecommerce',
                'label' => 'Sản phẩm mỗi trang',
                'description' => 'Số sản phẩm hiển thị trên mỗi trang',
                'is_public' => true,
                'sort_order' => 5,
            ],

            // Social Media
            [
                'key' => 'facebook_url',
                'value' => 'https://facebook.com/shopthoitrang',
                'type' => 'string',
                'group' => 'social',
                'label' => 'Facebook URL',
                'description' => 'Đường dẫn trang Facebook',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'instagram_url',
                'value' => 'https://instagram.com/shopthoitrang',
                'type' => 'string',
                'group' => 'social',
                'label' => 'Instagram URL',
                'description' => 'Đường dẫn trang Instagram',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'youtube_url',
                'value' => 'https://youtube.com/shopthoitrang',
                'type' => 'string',
                'group' => 'social',
                'label' => 'YouTube URL',
                'description' => 'Đường dẫn kênh YouTube',
                'is_public' => true,
                'sort_order' => 3,
            ],

            // Email Settings
            [
                'key' => 'smtp_host',
                'value' => 'smtp.gmail.com',
                'type' => 'string',
                'group' => 'email',
                'label' => 'SMTP Host',
                'description' => 'Máy chủ SMTP',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'smtp_port',
                'value' => '587',
                'type' => 'integer',
                'group' => 'email',
                'label' => 'SMTP Port',
                'description' => 'Cổng SMTP',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'smtp_username',
                'value' => '',
                'type' => 'string',
                'group' => 'email',
                'label' => 'SMTP Username',
                'description' => 'Tên đăng nhập SMTP',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'smtp_password',
                'value' => '',
                'type' => 'string',
                'group' => 'email',
                'label' => 'SMTP Password',
                'description' => 'Mật khẩu SMTP',
                'is_public' => false,
                'sort_order' => 4,
            ]
        ];

        foreach ($settings as $setting) {
            $setting['created_at'] = Carbon::now();
            $setting['updated_at'] = Carbon::now();
            DB::table('settings')->insert($setting);
        }
    }
}
