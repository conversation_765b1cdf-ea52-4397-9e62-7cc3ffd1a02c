<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class BannerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('banners')->insert([
            [
                'title' => 'Bộ sưu tập mùa hè 2024',
                'description' => 'Khám phá những xu hướng thời trang mới nhất cho mùa hè năm nay',
                'image' => 'banners/summer-2024-collection.jpg',
                'mobile_image' => 'banners/summer-2024-collection-mobile.jpg',
                'link_url' => '/collections/summer-2024',
                'link_text' => 'Khám phá ngay',
                'open_in_new_tab' => false,
                'type' => 'slider',
                'position' => 'home_slider',
                'is_active' => true,
                'sort_order' => 1,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMonths(3),
                'views_count' => 0,
                'clicks_count' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Giảm giá 50% toàn bộ đầm nữ',
                'description' => 'Cơ hội vàng để sở hữu những chiếc đầm đẹp với giá ưu đãi',
                'image' => 'banners/dress-sale-50.jpg',
                'mobile_image' => 'banners/dress-sale-50-mobile.jpg',
                'link_url' => '/categories/dam-nu',
                'link_text' => 'Mua ngay',
                'open_in_new_tab' => false,
                'type' => 'slider',
                'position' => 'home_slider',
                'is_active' => true,
                'sort_order' => 2,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addDays(30),
                'views_count' => 0,
                'clicks_count' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Thời trang nam công sở',
                'description' => 'Lịch lãm và chuyên nghiệp với bộ sưu tập thời trang nam công sở',
                'image' => 'banners/men-office-wear.jpg',
                'mobile_image' => 'banners/men-office-wear-mobile.jpg',
                'link_url' => '/categories/thoi-trang-nam',
                'link_text' => 'Xem thêm',
                'open_in_new_tab' => false,
                'type' => 'slider',
                'position' => 'home_slider',
                'is_active' => true,
                'sort_order' => 3,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMonths(2),
                'views_count' => 0,
                'clicks_count' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Miễn phí vận chuyển',
                'description' => 'Miễn phí ship toàn quốc cho đơn hàng từ 500k',
                'image' => 'banners/free-shipping.jpg',
                'mobile_image' => 'banners/free-shipping-mobile.jpg',
                'link_url' => '/products',
                'link_text' => 'Mua sắm ngay',
                'open_in_new_tab' => false,
                'type' => 'banner',
                'position' => 'home_promotion',
                'is_active' => true,
                'sort_order' => 1,
                'starts_at' => Carbon::now(),
                'expires_at' => null,
                'target_pages' => null,
                'target_categories' => null,
                'target_user_groups' => null,
                'views_count' => 0,
                'clicks_count' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Đăng ký nhận ưu đãi',
                'description' => 'Nhận ngay mã giảm giá 10% khi đăng ký thành viên',
                'image' => 'banners/newsletter-signup.jpg',
                'mobile_image' => 'banners/newsletter-signup-mobile.jpg',
                'link_url' => '/register',
                'link_text' => 'Đăng ký ngay',
                'open_in_new_tab' => false,
                'type' => 'popup',
                'position' => 'popup',
                'is_active' => true,
                'sort_order' => 1,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMonths(6),
                'target_pages' => json_encode(['home', 'products']),
                'views_count' => 0,
                'clicks_count' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Flash Sale cuối tuần',
                'description' => 'Giảm giá sốc 70% chỉ trong 48h',
                'image' => 'banners/flash-sale-weekend.jpg',
                'mobile_image' => 'banners/flash-sale-weekend-mobile.jpg',
                'link_url' => '/flash-sale',
                'link_text' => 'Mua ngay',
                'open_in_new_tab' => false,
                'type' => 'notification',
                'position' => 'top_bar',
                'is_active' => true,
                'sort_order' => 1,
                'starts_at' => Carbon::now()->startOfWeek()->addDays(5), // Friday
                'expires_at' => Carbon::now()->startOfWeek()->addDays(6)->endOfDay(), // Sunday
                'target_pages' => null,
                'target_categories' => null,
                'target_user_groups' => null,
                'views_count' => 0,
                'clicks_count' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Bộ sưu tập phụ kiện',
                'description' => 'Hoàn thiện phong cách với bộ sưu tập phụ kiện đa dạng',
                'image' => 'banners/accessories-collection.jpg',
                'mobile_image' => 'banners/accessories-collection-mobile.jpg',
                'link_url' => '/categories/phu-kien',
                'link_text' => 'Khám phá',
                'open_in_new_tab' => false,
                'type' => 'banner',
                'position' => 'sidebar',
                'is_active' => true,
                'sort_order' => 1,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMonths(4),
                'target_pages' => json_encode(['products', 'categories']),
                'target_categories' => null,
                'target_user_groups' => null,
                'views_count' => 0,
                'clicks_count' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ]);
    }
}
