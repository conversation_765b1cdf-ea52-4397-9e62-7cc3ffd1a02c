<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CouponSeederFixed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('coupons')->insert([
            [
                'code' => 'WELCOME10',
                'name' => 'Chào mừng khách hàng mới',
                'description' => 'Giảm 10% cho đơn hàng đầu tiên',
                'type' => 'percentage',
                'value' => 10.00,
                'minimum_amount' => 200000,
                'maximum_discount' => 100000,
                'usage_limit' => null,
                'usage_limit_per_customer' => 1,
                'used_count' => 0,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMonths(6),
                'first_order_only' => true,
                'customer_groups' => null,
                'is_active' => true,
                'is_public' => true,
                'combine_with_other_coupons' => false,
                'auto_apply' => false,
                'priority' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'code' => 'FREESHIP',
                'name' => 'Miễn phí vận chuyển',
                'description' => 'Miễn phí vận chuyển cho mọi đơn hàng',
                'type' => 'free_shipping',
                'value' => 0,
                'minimum_amount' => 300000,
                'maximum_discount' => null,
                'usage_limit' => 1000,
                'usage_limit_per_customer' => 5,
                'used_count' => 0,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMonths(3),
                'first_order_only' => false,
                'customer_groups' => null,
                'is_active' => true,
                'is_public' => true,
                'combine_with_other_coupons' => true,
                'auto_apply' => false,
                'priority' => 2,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'code' => 'SUMMER50',
                'name' => 'Khuyến mãi mùa hè',
                'description' => 'Giảm 50,000đ cho đơn hàng từ 500,000đ',
                'type' => 'fixed',
                'value' => 50000,
                'minimum_amount' => 500000,
                'maximum_discount' => null,
                'usage_limit' => 500,
                'usage_limit_per_customer' => 3,
                'used_count' => 0,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMonths(2),
                'first_order_only' => false,
                'customer_groups' => null,
                'is_active' => true,
                'is_public' => true,
                'combine_with_other_coupons' => false,
                'auto_apply' => false,
                'priority' => 3,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ]);
    }
}
