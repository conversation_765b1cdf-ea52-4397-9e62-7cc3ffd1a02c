<?php

/**
 * Script để setup database mới cho trang web bán hàng chuẩn SEO
 * 
 * Chạy script này để:
 * 1. <PERSON><PERSON><PERSON> tất cả bảng mới
 * 2. Seed dữ liệu mẫu
 * 3. <PERSON><PERSON><PERSON> tra tính toàn vẹn dữ liệu
 */

echo "🚀 Bắt đầu setup database mới cho Shop Thời Trang...\n\n";

// Danh sách migration files theo thứ tự
$migrations = [
    '2024_12_20_000001_create_new_users_table.php',
    '2024_12_20_000002_create_new_categories_table.php', 
    '2024_12_20_000003_create_brands_table.php',
    '2024_12_20_000004_create_new_products_table.php',
    '2024_12_20_000005_create_product_variants_table.php',
    '2024_12_20_000006_create_product_categories_table.php',
    '2024_12_20_000007_create_new_orders_table.php',
    '2024_12_20_000008_create_order_items_table.php',
    '2024_12_20_000009_create_carts_table.php',
    '2024_12_20_000010_create_reviews_table.php',
    '2024_12_20_000011_create_coupons_table.php',
    '2024_12_20_000012_create_wishlists_table.php',
    '2024_12_20_000013_create_product_tags_table.php',
    '2024_12_20_000014_create_shipping_zones_table.php',
    '2024_12_20_000015_create_payment_methods_table.php',
    '2024_12_20_000016_create_pages_table.php',
    '2024_12_20_000017_create_blogs_table.php',
    '2024_12_20_000018_create_seo_redirects_table.php',
    '2024_12_20_000019_create_settings_table.php',
    '2024_12_20_000020_create_banners_newsletters_table.php',
];

// Danh sách seeders theo thứ tự
$seeders = [
    'NewUserSeeder',
    'BrandSeeder', 
    'NewCategorySeeder',
    'TagSeeder',
    'NewProductSeeder',
    'ProductCategorySeeder',
    'ProductTagSeeder',
    'ProductVariantSeeder',
    'CouponSeeder',
    'ShippingZoneSeeder',
    'PaymentMethodSeeder',
    'PageSeeder',
    'BlogSeeder',
    'SettingSeeder',
    'BannerSeeder',
    'NewsletterSeeder',
    'SeoMetaSeeder',
];

function runCommand($command) {
    echo "📋 Chạy: $command\n";
    $output = [];
    $return_var = 0;
    exec($command . ' 2>&1', $output, $return_var);
    
    if ($return_var === 0) {
        echo "✅ Thành công!\n";
        return true;
    } else {
        echo "❌ Lỗi:\n";
        foreach ($output as $line) {
            echo "   $line\n";
        }
        return false;
    }
}

function checkRequirements() {
    echo "🔍 Kiểm tra yêu cầu hệ thống...\n";
    
    // Kiểm tra PHP version
    if (version_compare(PHP_VERSION, '8.0.0', '<')) {
        echo "❌ Cần PHP 8.0 trở lên. Hiện tại: " . PHP_VERSION . "\n";
        return false;
    }
    
    // Kiểm tra Laravel
    if (!file_exists('artisan')) {
        echo "❌ Không tìm thấy file artisan. Đảm bảo bạn đang ở thư mục root của Laravel.\n";
        return false;
    }
    
    // Kiểm tra database connection
    $output = [];
    exec('php artisan migrate:status 2>&1', $output, $return_var);
    if ($return_var !== 0) {
        echo "❌ Không thể kết nối database. Kiểm tra file .env\n";
        return false;
    }
    
    echo "✅ Hệ thống đáp ứng yêu cầu!\n\n";
    return true;
}

function runMigrations($migrations) {
    echo "📦 Chạy migrations...\n";
    
    foreach ($migrations as $migration) {
        $command = "php artisan migrate --path=/database/migrations/$migration";
        if (!runCommand($command)) {
            echo "❌ Migration thất bại: $migration\n";
            return false;
        }
    }
    
    echo "✅ Tất cả migrations đã chạy thành công!\n\n";
    return true;
}

function runSeeders($seeders) {
    echo "🌱 Chạy seeders...\n";
    
    foreach ($seeders as $seeder) {
        $command = "php artisan db:seed --class=$seeder";
        if (!runCommand($command)) {
            echo "❌ Seeder thất bại: $seeder\n";
            return false;
        }
    }
    
    echo "✅ Tất cả seeders đã chạy thành công!\n\n";
    return true;
}

function verifyData() {
    echo "🔍 Kiểm tra dữ liệu...\n";
    
    $checks = [
        "SELECT COUNT(*) as count FROM new_users" => "Users",
        "SELECT COUNT(*) as count FROM brands" => "Brands", 
        "SELECT COUNT(*) as count FROM new_categories" => "Categories",
        "SELECT COUNT(*) as count FROM new_products" => "Products",
        "SELECT COUNT(*) as count FROM product_variants" => "Product Variants",
        "SELECT COUNT(*) as count FROM coupons" => "Coupons",
        "SELECT COUNT(*) as count FROM settings" => "Settings",
    ];
    
    foreach ($checks as $query => $name) {
        $command = "php artisan tinker --execute=\"echo DB::select('$query')[0]->count;\"";
        $output = [];
        exec($command, $output);
        $count = trim(end($output));
        echo "   $name: $count records\n";
    }
    
    echo "✅ Kiểm tra dữ liệu hoàn tất!\n\n";
}

function showSummary() {
    echo "🎉 SETUP HOÀN TẤT!\n\n";
    echo "📊 Thông tin tài khoản:\n";
    echo "   👤 Admin: <EMAIL> / admin123\n";
    echo "   👤 Customer: <EMAIL> / 123456\n";
    echo "   👤 Staff: <EMAIL> / staff123\n\n";
    
    echo "🎫 Mã giảm giá có sẵn:\n";
    echo "   🎁 WELCOME10 - Giảm 10% cho khách hàng mới\n";
    echo "   🚚 FREESHIP - Miễn phí vận chuyển\n";
    echo "   💰 SUMMER50 - Giảm 50,000đ\n";
    echo "   ⭐ VIP20 - Giảm 20% cho VIP\n";
    echo "   ⚡ FLASH15 - Flash sale 15%\n\n";
    
    echo "📚 Tài liệu:\n";
    echo "   📖 Đọc file database/README_NEW_DATABASE.md để biết thêm chi tiết\n";
    echo "   🔧 Cấu hình SEO trong bảng 'settings'\n";
    echo "   🎨 Tùy chỉnh banners trong bảng 'banners'\n\n";
    
    echo "🚀 Bây giờ bạn có thể:\n";
    echo "   1. Tạo Models cho các bảng mới\n";
    echo "   2. Cập nhật Controllers\n";
    echo "   3. Thiết kế giao diện admin\n";
    echo "   4. Tích hợp SEO features\n\n";
    
    echo "✨ Chúc bạn thành công với trang web bán hàng mới!\n";
}

// Main execution
try {
    // Kiểm tra yêu cầu
    if (!checkRequirements()) {
        exit(1);
    }
    
    // Hỏi xác nhận
    echo "⚠️  CẢNH BÁO: Script này sẽ tạo các bảng mới trong database.\n";
    echo "Bạn có chắc chắn muốn tiếp tục? (y/N): ";
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    fclose($handle);
    
    if (trim(strtolower($line)) !== 'y') {
        echo "❌ Đã hủy bỏ.\n";
        exit(0);
    }
    
    // Chạy migrations
    if (!runMigrations($migrations)) {
        echo "❌ Setup thất bại tại bước migration.\n";
        exit(1);
    }
    
    // Chạy seeders
    if (!runSeeders($seeders)) {
        echo "❌ Setup thất bại tại bước seeding.\n";
        exit(1);
    }
    
    // Kiểm tra dữ liệu
    verifyData();
    
    // Hiển thị tóm tắt
    showSummary();
    
} catch (Exception $e) {
    echo "❌ Lỗi: " . $e->getMessage() . "\n";
    exit(1);
}
