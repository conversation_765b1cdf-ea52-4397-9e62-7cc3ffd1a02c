<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('tags')->insert([
            [
                'name' => 'Mới nhất',
                'slug' => 'moi-nhat',
                'description' => 'Sản phẩm mới nhất trong bộ sưu tập',
                'color' => '#ff6b6b',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Bán chạy',
                'slug' => 'ban-chay',
                'description' => 'Sản phẩm bán chạy nhất',
                'color' => '#4ecdc4',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Giảm giá',
                'slug' => 'giam-gia',
                'description' => 'Sản phẩm đang được giảm giá',
                'color' => '#45b7d1',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Cao cấp',
                'slug' => 'cao-cap',
                'description' => 'Sản phẩm cao cấp, chất lượng premium',
                'color' => '#f9ca24',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Thời trang công sở',
                'slug' => 'thoi-trang-cong-so',
                'description' => 'Phù hợp cho môi trường công sở',
                'color' => '#6c5ce7',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Casual',
                'slug' => 'casual',
                'description' => 'Phong cách thoải mái, dạo phố',
                'color' => '#a29bfe',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Dự tiệc',
                'slug' => 'du-tiec',
                'description' => 'Phù hợp cho các buổi tiệc, sự kiện',
                'color' => '#fd79a8',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Thể thao',
                'slug' => 'the-thao',
                'description' => 'Trang phục thể thao, tập luyện',
                'color' => '#00b894',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Hàn Quốc',
                'slug' => 'han-quoc',
                'description' => 'Phong cách thời trang Hàn Quốc',
                'color' => '#e17055',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Vintage',
                'slug' => 'vintage',
                'description' => 'Phong cách cổ điển, retro',
                'color' => '#fdcb6e',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ]);
    }
}
