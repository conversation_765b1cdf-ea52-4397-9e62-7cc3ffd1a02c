<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->longText('content');
            $table->text('excerpt')->nullable();
            
            // Page settings
            $table->enum('status', ['draft', 'published', 'private'])->default('draft');
            $table->string('template')->default('default'); // Template file to use
            $table->boolean('show_in_menu')->default(false);
            $table->integer('menu_order')->default(0);
            
            // SEO fields
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->string('canonical_url')->nullable();
            $table->json('og_tags')->nullable(); // Open Graph tags
            
            // Featured image
            $table->string('featured_image')->nullable();
            $table->text('featured_image_alt')->nullable();
            
            // Author and dates
            $table->foreignId('author_id')->constrained('new_users')->onDelete('cascade');
            $table->timestamp('published_at')->nullable();
            
            // Page hierarchy
            $table->foreignId('parent_id')->nullable()->constrained('pages')->onDelete('set null');
            
            // Custom fields
            $table->json('custom_fields')->nullable();
            
            // Analytics
            $table->integer('views_count')->default(0);
            
            $table->timestamps();
            
            // Indexes
            $table->index(['slug', 'status']);
            $table->index(['status', 'published_at']);
            $table->index(['show_in_menu', 'menu_order']);
            $table->index('parent_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pages');
    }
};
