<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            
            // Discount type and value
            $table->enum('type', ['fixed', 'percentage', 'free_shipping', 'buy_x_get_y']);
            $table->decimal('value', 12, 2); // Amount or percentage
            $table->decimal('minimum_amount', 12, 2)->nullable(); // Minimum order amount
            $table->decimal('maximum_discount', 12, 2)->nullable(); // Maximum discount for percentage coupons
            
            // Usage limits
            $table->integer('usage_limit')->nullable(); // Total usage limit
            $table->integer('usage_limit_per_customer')->nullable();
            $table->integer('used_count')->default(0);
            
            // Date restrictions
            $table->timestamp('starts_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            
            // Product/Category restrictions
            $table->json('applicable_products')->nullable(); // Product IDs
            $table->json('applicable_categories')->nullable(); // Category IDs
            $table->json('excluded_products')->nullable();
            $table->json('excluded_categories')->nullable();
            
            // Customer restrictions
            $table->json('applicable_customers')->nullable(); // Customer IDs
            $table->json('customer_groups')->nullable(); // Customer group restrictions
            $table->boolean('first_order_only')->default(false);
            
            // Buy X Get Y settings (for BXGY coupons)
            $table->json('bxgy_settings')->nullable(); // Buy X quantity, Get Y quantity, etc.
            
            // Status and settings
            $table->boolean('is_active')->default(true);
            $table->boolean('is_public')->default(true); // Can be found by customers
            $table->boolean('combine_with_other_coupons')->default(false);
            
            // Auto-apply settings
            $table->boolean('auto_apply')->default(false);
            $table->integer('priority')->default(0); // For auto-apply ordering
            
            $table->timestamps();
            
            // Indexes
            $table->index(['code', 'is_active']);
            $table->index(['is_active', 'starts_at', 'expires_at']);
            $table->index(['auto_apply', 'priority']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupons');
    }
};
