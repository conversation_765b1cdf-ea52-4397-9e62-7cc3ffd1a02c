<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class NewUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('new_users')->insert([
            [
                'first_name' => 'Admin',
                'last_name' => 'System',
                'email' => '<EMAIL>',
                'email_verified_at' => Carbon::now(),
                'phone' => '0123456789',
                'date_of_birth' => '1990-01-01',
                'gender' => 'male',
                'password' => Hash::make('admin123'),
                'address' => '123 Nguyễn Hu<PERSON>, Quận 1',
                'city' => 'TP. Hồ Chí <PERSON>',
                'state' => 'TP. Hồ Chí Minh',
                'postal_code' => '70000',
                'country' => 'VN',
                'status' => 'active',
                'is_verified' => true,
                'role' => 'admin',
                'accepts_marketing' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'first_name' => 'Nguyễn',
                'last_name' => 'Văn A',
                'email' => '<EMAIL>',
                'email_verified_at' => Carbon::now(),
                'phone' => '0987654321',
                'date_of_birth' => '1995-05-15',
                'gender' => 'male',
                'password' => Hash::make('123456'),
                'address' => '456 Lê Lợi, Quận 3',
                'city' => 'TP. Hồ Chí Minh',
                'state' => 'TP. Hồ Chí Minh',
                'postal_code' => '70000',
                'country' => 'VN',
                'status' => 'active',
                'is_verified' => true,
                'role' => 'customer',
                'accepts_marketing' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'first_name' => 'Trần',
                'last_name' => 'Thị B',
                'email' => '<EMAIL>',
                'email_verified_at' => Carbon::now(),
                'phone' => '0912345678',
                'date_of_birth' => '1992-08-20',
                'gender' => 'female',
                'password' => Hash::make('123456'),
                'address' => '789 Võ Văn Tần, Quận 3',
                'city' => 'TP. Hồ Chí Minh',
                'state' => 'TP. Hồ Chí Minh',
                'postal_code' => '70000',
                'country' => 'VN',
                'status' => 'active',
                'is_verified' => true,
                'role' => 'customer',
                'accepts_marketing' => false,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'first_name' => 'Staff',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'email_verified_at' => Carbon::now(),
                'phone' => '0901234567',
                'date_of_birth' => '1988-03-10',
                'gender' => 'male',
                'password' => Hash::make('staff123'),
                'address' => '321 Pasteur, Quận 1',
                'city' => 'TP. Hồ Chí Minh',
                'state' => 'TP. Hồ Chí Minh',
                'postal_code' => '70000',
                'country' => 'VN',
                'status' => 'active',
                'is_verified' => true,
                'role' => 'staff',
                'accepts_marketing' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ]);
    }
}
