<?php

/**
 * <PERSON>ript để chạy seeders theo đúng thứ tự
 */

echo "🌱 Chạy seeders theo thứ tự...\n\n";

$seeders = [
    // Bước 1: <PERSON><PERSON> liệ<PERSON> cơ bản
    'NewUserSeeder' => 'Tạo users',
    'BrandSeeder' => 'Tạo brands', 
    'NewCategorySeeder' => 'Tạo categories',
    'TagSeeder' => 'Tạo tags',
    
    // Bước 2: Sản phẩm
    'NewProductSeeder' => 'Tạo products',
    
    // Bước 3: <PERSON>uan hệ sản phẩm
    'ProductCategorySeeder' => 'Liên kết product-category',
    'ProductTagSeeder' => 'Liên kết product-tag',
    'ProductVariantSeeder' => 'Tạo product variants',
    
    // Bước 4: E-commerce
    'CouponSeeder' => 'Tạo coupons',
    'ShippingZoneSeeder' => 'Tạo shipping zones',
    'PaymentMethodSeeder' => 'Tạo payment methods',
    
    // Bước 5: CMS
    'PageSeederFixed' => 'Tạo pages',
    'BlogSeeder' => 'Tạo blog posts',
    'SettingSeeder' => 'Tạo settings',
    
    // Bước 6: Marketing
    'BannerSeeder' => 'Tạo banners',
    'NewsletterSeeder' => 'Tạo newsletter data',
    'SeoMetaSeeder' => 'Tạo SEO meta',
];

function runSeeder($seeder, $description) {
    echo "📋 $description ($seeder)...\n";
    $command = "php artisan db:seed --class=$seeder 2>&1";
    $output = [];
    $return_var = 0;
    exec($command, $output, $return_var);
    
    if ($return_var === 0) {
        echo "✅ Thành công!\n\n";
        return true;
    } else {
        echo "❌ Lỗi:\n";
        foreach ($output as $line) {
            echo "   $line\n";
        }
        echo "\n";
        return false;
    }
}

$success = 0;
$total = count($seeders);

foreach ($seeders as $seeder => $description) {
    if (runSeeder($seeder, $description)) {
        $success++;
    } else {
        echo "⚠️  Tiếp tục với seeder tiếp theo...\n\n";
    }
}

echo "📊 Kết quả: $success/$total seeders thành công\n\n";

// Kiểm tra dữ liệu cuối cùng
echo "🔍 Kiểm tra dữ liệu:\n";
$checks = [
    'new_users' => 'Users',
    'brands' => 'Brands',
    'new_categories' => 'Categories', 
    'new_products' => 'Products',
    'product_variants' => 'Product Variants',
    'coupons' => 'Coupons',
    'settings' => 'Settings',
];

foreach ($checks as $table => $name) {
    $command = "php artisan tinker --execute=\"echo DB::table('$table')->count();\"";
    $output = [];
    exec($command, $output);
    $count = trim(end($output));
    echo "   $name: $count records\n";
}

echo "\n✨ Hoàn tất!\n";
