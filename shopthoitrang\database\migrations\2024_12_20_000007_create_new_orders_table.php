<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('new_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->foreignId('user_id')->nullable()->constrained('new_users')->onDelete('set null');
            
            // Order status
            $table->enum('status', [
                'pending', 'confirmed', 'processing', 'shipped', 
                'delivered', 'cancelled', 'refunded', 'returned'
            ])->default('pending');
            
            $table->enum('payment_status', [
                'pending', 'paid', 'partially_paid', 'refunded', 
                'partially_refunded', 'failed', 'cancelled'
            ])->default('pending');
            
            $table->enum('shipping_status', [
                'pending', 'processing', 'shipped', 'in_transit', 
                'delivered', 'failed', 'returned'
            ])->default('pending');
            
            // Customer information (stored for guest orders)
            $table->string('customer_email');
            $table->string('customer_phone')->nullable();
            $table->string('customer_first_name');
            $table->string('customer_last_name');
            
            // Billing address
            $table->json('billing_address'); // JSON with all billing details
            
            // Shipping address
            $table->json('shipping_address'); // JSON with all shipping details
            $table->boolean('same_as_billing')->default(true);
            
            // Order totals
            $table->decimal('subtotal', 12, 2); // Before tax and shipping
            $table->decimal('tax_amount', 12, 2)->default(0);
            $table->decimal('shipping_amount', 12, 2)->default(0);
            $table->decimal('discount_amount', 12, 2)->default(0);
            $table->decimal('total_amount', 12, 2);
            
            // Currency
            $table->string('currency', 3)->default('VND');
            $table->decimal('exchange_rate', 10, 4)->default(1); // For multi-currency support
            
            // Payment information
            $table->string('payment_method')->nullable(); // COD, Bank Transfer, Credit Card, etc.
            $table->string('payment_gateway')->nullable(); // VNPay, MoMo, PayPal, etc.
            $table->string('payment_transaction_id')->nullable();
            $table->json('payment_details')->nullable(); // Additional payment info
            
            // Shipping information
            $table->string('shipping_method')->nullable();
            $table->string('tracking_number')->nullable();
            $table->string('shipping_carrier')->nullable();
            $table->timestamp('shipped_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            
            // Coupon/Discount
            $table->string('coupon_code')->nullable();
            $table->json('applied_discounts')->nullable(); // Details of applied discounts
            
            // Order notes
            $table->text('notes')->nullable(); // Customer notes
            $table->text('admin_notes')->nullable(); // Internal notes
            
            // Fulfillment
            $table->boolean('is_gift')->default(false);
            $table->text('gift_message')->nullable();
            $table->json('tags')->nullable(); // Order tags for organization
            
            // Timestamps
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->string('cancelled_reason')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['user_id', 'status']);
            $table->index(['status', 'created_at']);
            $table->index(['payment_status', 'created_at']);
            $table->index(['shipping_status', 'created_at']);
            $table->index('customer_email');
            $table->index('tracking_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('new_orders');
    }
};
