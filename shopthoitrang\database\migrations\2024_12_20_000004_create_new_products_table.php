<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('new_products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->text('short_description')->nullable();
            
            // Product identification
            $table->string('sku')->unique();
            $table->string('barcode')->nullable();
            $table->string('model_number')->nullable();
            
            // Relationships
            $table->foreignId('brand_id')->nullable()->constrained('brands')->onDelete('set null');
            
            // Pricing
            $table->decimal('price', 12, 2);
            $table->decimal('compare_price', 12, 2)->nullable(); // Original price for discounts
            $table->decimal('cost_price', 12, 2)->nullable(); // Cost for profit calculation
            
            // Inventory management
            $table->boolean('track_quantity')->default(true);
            $table->integer('quantity')->default(0);
            $table->integer('min_quantity')->default(0); // Low stock alert
            $table->boolean('continue_selling_when_out_of_stock')->default(false);
            
            // Physical properties
            $table->decimal('weight', 8, 2)->nullable(); // in grams
            $table->decimal('length', 8, 2)->nullable(); // in cm
            $table->decimal('width', 8, 2)->nullable(); // in cm
            $table->decimal('height', 8, 2)->nullable(); // in cm
            
            // Product media
            $table->json('images')->nullable(); // Array of image URLs
            $table->string('featured_image')->nullable();
            $table->json('videos')->nullable(); // Array of video URLs
            
            // Product status
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_digital')->default(false);
            $table->enum('status', ['draft', 'active', 'archived'])->default('draft');
            
            // SEO fields
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->string('canonical_url')->nullable();
            $table->json('og_tags')->nullable(); // Open Graph tags
            
            // Product attributes
            $table->json('attributes')->nullable(); // Custom attributes like color, size, material
            $table->json('specifications')->nullable(); // Technical specifications
            
            // Shipping
            $table->boolean('requires_shipping')->default(true);
            $table->decimal('shipping_weight', 8, 2)->nullable();
            $table->json('shipping_dimensions')->nullable();
            
            // Tax and legal
            $table->boolean('taxable')->default(true);
            $table->string('tax_class')->nullable();
            $table->string('origin_country')->default('VN');
            
            // Statistics and analytics
            $table->integer('views_count')->default(0);
            $table->integer('sales_count')->default(0);
            $table->decimal('average_rating', 3, 2)->default(0);
            $table->integer('reviews_count')->default(0);
            $table->integer('wishlist_count')->default(0);
            
            // Dates
            $table->timestamp('published_at')->nullable();
            $table->timestamp('available_from')->nullable();
            $table->timestamp('available_until')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['slug', 'is_active']);
            $table->index(['brand_id', 'is_active']);
            $table->index(['is_featured', 'is_active']);
            $table->index(['status', 'is_active']);
            $table->index('sku');
            $table->index('price');
            $table->index('published_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('new_products');
    }
};
