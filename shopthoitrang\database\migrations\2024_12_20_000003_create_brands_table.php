<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('brands', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->text('short_description')->nullable();
            
            // Brand media
            $table->string('logo')->nullable();
            $table->string('banner_image')->nullable();
            $table->json('gallery')->nullable(); // Additional brand images
            
            // Contact information
            $table->string('website')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            
            // Social media
            $table->json('social_links')->nullable(); // Facebook, Instagram, Twitter, etc.
            
            // Brand settings
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->integer('sort_order')->default(0);
            
            // SEO fields
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->string('canonical_url')->nullable();
            $table->json('og_tags')->nullable(); // Open Graph tags
            
            // Business information
            $table->string('country_of_origin')->nullable();
            $table->year('founded_year')->nullable();
            $table->text('story')->nullable(); // Brand story/history
            
            // Commission settings (for marketplace)
            $table->decimal('commission_rate', 5, 2)->nullable();
            
            // Statistics
            $table->integer('products_count')->default(0);
            $table->decimal('average_rating', 3, 2)->default(0);
            $table->integer('reviews_count')->default(0);
            
            $table->timestamps();
            
            // Indexes
            $table->index(['slug', 'is_active']);
            $table->index(['is_featured', 'is_active']);
            $table->index('sort_order');
            $table->index('country_of_origin');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('brands');
    }
};
