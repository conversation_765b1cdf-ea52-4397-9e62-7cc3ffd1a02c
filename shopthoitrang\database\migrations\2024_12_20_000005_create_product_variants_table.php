<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('new_products')->onDelete('cascade');
            
            // Variant identification
            $table->string('title'); // e.g., "Red / Large"
            $table->string('sku')->unique();
            $table->string('barcode')->nullable();
            
            // Pricing
            $table->decimal('price', 12, 2);
            $table->decimal('compare_price', 12, 2)->nullable();
            $table->decimal('cost_price', 12, 2)->nullable();
            
            // Inventory
            $table->integer('quantity')->default(0);
            $table->integer('min_quantity')->default(0);
            $table->boolean('track_quantity')->default(true);
            
            // Physical properties
            $table->decimal('weight', 8, 2)->nullable();
            $table->decimal('length', 8, 2)->nullable();
            $table->decimal('width', 8, 2)->nullable();
            $table->decimal('height', 8, 2)->nullable();
            
            // Variant options (up to 3 options like Color, Size, Material)
            $table->string('option1_name')->nullable(); // e.g., "Color"
            $table->string('option1_value')->nullable(); // e.g., "Red"
            $table->string('option2_name')->nullable(); // e.g., "Size"
            $table->string('option2_value')->nullable(); // e.g., "Large"
            $table->string('option3_name')->nullable(); // e.g., "Material"
            $table->string('option3_value')->nullable(); // e.g., "Cotton"
            
            // Media
            $table->string('image')->nullable();
            $table->json('images')->nullable(); // Additional images for this variant
            
            // Status
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false); // Default variant for the product
            
            // Shipping
            $table->decimal('shipping_weight', 8, 2)->nullable();
            $table->json('shipping_dimensions')->nullable();
            
            // Position for ordering
            $table->integer('position')->default(0);
            
            // Statistics
            $table->integer('sales_count')->default(0);
            
            $table->timestamps();
            
            // Indexes
            $table->index(['product_id', 'is_active']);
            $table->index('sku');
            $table->index(['option1_value', 'option2_value', 'option3_value']);
            $table->index('position');
            $table->unique(['product_id', 'option1_value', 'option2_value', 'option3_value'], 'unique_variant_options');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_variants');
    }
};
