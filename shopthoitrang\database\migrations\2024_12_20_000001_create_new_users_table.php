<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('new_users', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('phone')->nullable();
            $table->date('date_of_birth')->nullable();
            $table->enum('gender', ['male', 'female', 'other'])->nullable();
            $table->string('avatar')->nullable();
            $table->string('password');
            
            // Address information
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->default('VN');
            
            // Social login
            $table->string('social_login_provider')->nullable();
            $table->string('social_login_id')->nullable();
            
            // User preferences and settings
            $table->json('preferences')->nullable(); // JSON field for user preferences
            $table->string('language')->default('vi');
            $table->string('timezone')->default('Asia/Ho_Chi_Minh');
            
            // Account status
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->boolean('is_verified')->default(false);
            $table->timestamp('last_login_at')->nullable();
            
            // Role and permissions
            $table->enum('role', ['customer', 'admin', 'staff', 'vendor'])->default('customer');
            $table->json('permissions')->nullable();
            
            // Marketing preferences
            $table->boolean('accepts_marketing')->default(true);
            $table->boolean('accepts_sms_marketing')->default(false);
            
            $table->rememberToken();
            $table->timestamps();
            
            // Indexes
            $table->index(['email', 'status']);
            $table->index(['role', 'status']);
            $table->index('last_login_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('new_users');
    }
};
