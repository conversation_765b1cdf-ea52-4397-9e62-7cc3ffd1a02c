<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('new_orders')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('new_products')->onDelete('cascade');
            $table->foreignId('product_variant_id')->nullable()->constrained('product_variants')->onDelete('set null');
            
            // Product snapshot (stored at time of order)
            $table->string('product_name');
            $table->string('product_sku');
            $table->text('product_description')->nullable();
            $table->string('product_image')->nullable();
            
            // Variant snapshot
            $table->string('variant_title')->nullable();
            $table->string('variant_sku')->nullable();
            $table->json('variant_options')->nullable(); // Color, Size, etc.
            
            // Quantity and pricing
            $table->integer('quantity');
            $table->decimal('unit_price', 12, 2); // Price per unit at time of order
            $table->decimal('total_price', 12, 2); // quantity * unit_price
            
            // Discounts applied to this item
            $table->decimal('discount_amount', 12, 2)->default(0);
            $table->json('applied_discounts')->nullable();
            
            // Tax information
            $table->decimal('tax_rate', 5, 4)->default(0); // Tax rate as decimal (0.1 = 10%)
            $table->decimal('tax_amount', 12, 2)->default(0);
            
            // Physical properties (for shipping calculation)
            $table->decimal('weight', 8, 2)->nullable();
            $table->json('dimensions')->nullable();
            
            // Fulfillment status
            $table->enum('fulfillment_status', [
                'pending', 'processing', 'shipped', 'delivered', 
                'cancelled', 'returned', 'refunded'
            ])->default('pending');
            
            // Return/Refund information
            $table->integer('returned_quantity')->default(0);
            $table->decimal('refunded_amount', 12, 2)->default(0);
            $table->text('return_reason')->nullable();
            
            // Custom fields for special products
            $table->json('custom_fields')->nullable(); // For personalized products
            $table->text('special_instructions')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['order_id', 'fulfillment_status']);
            $table->index(['product_id', 'created_at']);
            $table->index('product_variant_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
