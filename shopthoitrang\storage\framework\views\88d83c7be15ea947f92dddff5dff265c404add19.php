 <!-- <PERSON><PERSON><PERSON> n<PERSON>i với layout app.blade.php -->

<?php $__env->startSection('title', 'Đăng nhập'); ?> <!-- Đặt tiêu đề cho trang login -->

<?php $__env->startSection('content'); ?>
    <div class="login-form-container">
        <div class="form-head">Đ<PERSON>ng nhập</div>

        
        <?php if(Session::has('message')): ?>
            <div class="error-message"><?php echo e(Session::get('message')); ?></div>
            <?php Session::put('message', null); ?>
        <?php endif; ?>

        <form action="<?php echo e(route('user.cus_login')); ?>" method="post" id="frmLogin" onsubmit="return validate();">
            <?php echo csrf_field(); ?>

            
            <div class="field-column">
                <div>
                    <label for="email">Email</label><span id="user_info" class="error-info"></span>
                </div>
                <div>
                    <input name="email" id="user_name" type="email" class="demo-input-box" placeholder="Nhập email">
                    <?php if($errors->has('email')): ?>
                        <span class="text-danger"><?php echo e($errors->first('email')); ?></span>
                    <?php endif; ?>
                </div>
            </div>

            
            <div class="field-column">
                <div>
                    <label for="password">Mật khẩu</label><span id="password_info" class="error-info"></span>
                </div>
                <div>
                    <input name="password" id="password" type="password" class="demo-input-box" placeholder="Nhập mật khẩu">
                    <?php if($errors->has('password')): ?>
                        <span class="text-danger"><?php echo e($errors->first('password')); ?></span>
                    <?php endif; ?>
                </div>
            </div>

            
            <div class="field-column">
                <div>
                    <input type="submit" name="login" value="Đăng nhập" class="btnLogin">
                </div>
            </div>

            
            <div class="form-nav-row">
                <a href="#" class="form-link" onclick="alert('Chức năng quên mật khẩu đang được phát triển.'); return false;">Quên mật khẩu?</a>
            </div>
            <div class="login-row form-nav-row">
                <p>Bạn chưa có tài khoản?</p>
                <a href="<?php echo e(route('user.cus_register')); ?>" class="btn form-link">Tạo tài khoản</a>
            </div>
            <div class="login-row form-nav-row">
                <p><a href="<?php echo e(route('home.index')); ?>" class="form-link">Quay lại trang chủ</a></p>
            </div>
        </form>
    </div>

    
    <script>
        function validate() {
            var $valid = true;
            document.getElementById("user_info").innerHTML = "";
            document.getElementById("password_info").innerHTML = "";

            var userName = document.getElementById("user_name").value;
            var password = document.getElementById("password").value;

            if (userName == "") {
                document.getElementById("user_info").innerHTML = "required";
                $valid = false;
            }
            if (password == "") {
                document.getElementById("password_info").innerHTML = "required";
                $valid = false;
            }

            return $valid;
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/cus_login.blade.php ENDPATH**/ ?>