<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class BlogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create blog categories
        DB::table('blog_categories')->insert([
            [
                'name' => '<PERSON> hướng thời trang',
                'slug' => 'xu-huong-thoi-trang',
                'description' => 'Cập nhật những xu hướng thời trang mới nhất',
                'image' => 'blog-categories/xu-huong-thoi-trang.jpg',
                'is_active' => true,
                'sort_order' => 1,
                'meta_title' => 'Xu hướng thời trang - Blog Shop Thời Trang',
                'meta_description' => 'Cập nhật những xu hướng thời trang hot nhất hiện nay',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => '<PERSON><PERSON><PERSON> đồ',
                'slug' => 'phoi-do',
                'description' => 'Hướng dẫn phối đồ đẹp cho mọi dịp',
                'image' => 'blog-categories/phoi-do.jpg',
                'is_active' => true,
                'sort_order' => 2,
                'meta_title' => 'Phối đồ - Blog Shop Thời Trang',
                'meta_description' => 'Học cách phối đồ đẹp và phù hợp với từng dịp',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Chăm sóc quần áo',
                'slug' => 'cham-soc-quan-ao',
                'description' => 'Mẹo chăm sóc và bảo quản quần áo',
                'image' => 'blog-categories/cham-soc-quan-ao.jpg',
                'is_active' => true,
                'sort_order' => 3,
                'meta_title' => 'Chăm sóc quần áo - Blog Shop Thời Trang',
                'meta_description' => 'Mẹo hay để chăm sóc và bảo quản quần áo bền đẹp',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ]);

        // Create blog tags
        DB::table('blog_tags')->insert([
            [
                'name' => 'Thời trang 2024',
                'slug' => 'thoi-trang-2024',
                'description' => 'Xu hướng thời trang năm 2024',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Phong cách Hàn Quốc',
                'slug' => 'phong-cach-han-quoc',
                'description' => 'Phong cách thời trang Hàn Quốc',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Thời trang công sở',
                'slug' => 'thoi-trang-cong-so',
                'description' => 'Thời trang phù hợp cho môi trường công sở',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Mẹo hay',
                'slug' => 'meo-hay',
                'description' => 'Những mẹo hay về thời trang',
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ]);

        // Create blog posts
        $posts = [
            [
                'title' => '10 xu hướng thời trang nổi bật năm 2024',
                'slug' => '10-xu-huong-thoi-trang-noi-bat-nam-2024',
                'content' => '<p>Năm 2024 đánh dấu sự trở lại của nhiều xu hướng thời trang thú vị và đột phá. Hãy cùng khám phá 10 xu hướng nổi bật nhất mà bạn không thể bỏ qua.</p>

<h3>1. Màu sắc tươi sáng</h3>
<p>Các tông màu neon và pastel đang thống trị sàn diễn thời trang 2024. Từ xanh lá cây neon đến hồng pastel nhẹ nhàng, những màu sắc này mang lại cảm giác tươi mới và năng động.</p>

<h3>2. Phong cách Y2K comeback</h3>
<p>Phong cách Y2K với những chi tiết metallic, áo crop top và quần low-rise đang quay trở lại mạnh mẽ, thu hút giới trẻ yêu thích sự nostalgic.</p>

<h3>3. Oversized blazer</h3>
<p>Áo blazer oversized không chỉ thoải mái mà còn tạo nên vẻ ngoài chuyên nghiệp và thời thượng, phù hợp cho cả môi trường công sở lẫn dạo phố.</p>

<p>Và còn nhiều xu hướng thú vị khác đang chờ bạn khám phá...</p>',
                'excerpt' => 'Khám phá 10 xu hướng thời trang nổi bật và không thể bỏ qua trong năm 2024',
                'status' => 'published',
                'is_featured' => true,
                'author_id' => 1,
                'blog_category_id' => 1,
                'featured_image' => 'blog/xu-huong-2024.jpg',
                'featured_image_alt' => 'Xu hướng thời trang 2024',
                'published_at' => Carbon::now()->subDays(5),
                'reading_time' => 5,
                'views_count' => 1250,
                'meta_title' => '10 xu hướng thời trang nổi bật năm 2024 | Blog Shop Thời Trang',
                'meta_description' => 'Cập nhật ngay 10 xu hướng thời trang hot nhất năm 2024. Từ màu sắc tươi sáng đến phong cách Y2K comeback.',
                'created_at' => Carbon::now()->subDays(5),
                'updated_at' => Carbon::now()->subDays(5),
            ],
            [
                'title' => 'Cách phối đồ công sở thanh lịch cho nữ',
                'slug' => 'cach-phoi-do-cong-so-thanh-lich-cho-nu',
                'content' => '<p>Phối đồ công sở không chỉ cần chuyên nghiệp mà còn phải thể hiện được phong cách cá nhân. Dưới đây là những gợi ý giúp bạn tạo nên những set đồ công sở hoàn hảo.</p>

<h3>1. Chọn trang phục cơ bản</h3>
<p>Đầu tư vào những món đồ cơ bản như áo sơ mi trắng, blazer đen, quần tây và chân váy bút chì. Đây là nền tảng cho mọi set đồ công sở.</p>

<h3>2. Kết hợp màu sắc hài hòa</h3>
<p>Sử dụng quy tắc 3 màu: chọn 1 màu chủ đạo, 1 màu phụ và 1 màu nhấn. Điều này giúp trang phục trông chuyên nghiệp và hài hòa.</p>

<h3>3. Phụ kiện tinh tế</h3>
<p>Thêm những phụ kiện như đồng hồ, túi xách và giày cao gót để hoàn thiện tổng thể. Nhớ giữ nguyên tắc "ít mà chất".</p>',
                'excerpt' => 'Hướng dẫn chi tiết cách phối đồ công sở thanh lịch và chuyên nghiệp cho phái nữ',
                'status' => 'published',
                'is_featured' => false,
                'author_id' => 1,
                'blog_category_id' => 2,
                'featured_image' => 'blog/phoi-do-cong-so.jpg',
                'featured_image_alt' => 'Phối đồ công sở cho nữ',
                'published_at' => Carbon::now()->subDays(10),
                'reading_time' => 4,
                'views_count' => 890,
                'meta_title' => 'Cách phối đồ công sở thanh lịch cho nữ | Blog Shop Thời Trang',
                'meta_description' => 'Học cách phối đồ công sở chuyên nghiệp và thanh lịch. Những tips hay để tạo nên phong cách công sở hoàn hảo.',
                'created_at' => Carbon::now()->subDays(10),
                'updated_at' => Carbon::now()->subDays(10),
            ],
            [
                'title' => 'Bí quyết chăm sóc quần áo để bền đẹp',
                'slug' => 'bi-quyet-cham-soc-quan-ao-de-ben-dep',
                'content' => '<p>Việc chăm sóc quần áo đúng cách không chỉ giúp trang phục bền đẹp mà còn tiết kiệm chi phí mua sắm. Dưới đây là những bí quyết hữu ích.</p>

<h3>1. Đọc kỹ nhãn chăm sóc</h3>
<p>Mỗi loại vải có cách chăm sóc riêng. Hãy luôn đọc kỹ nhãn hướng dẫn trước khi giặt để tránh làm hỏng quần áo.</p>

<h3>2. Phân loại quần áo trước khi giặt</h3>
<p>Tách riêng quần áo màu tối và màu sáng, đồ cotton và đồ len để tránh phai màu và co rút.</p>

<h3>3. Sử dụng nước giặt phù hợp</h3>
<p>Chọn nước giặt chuyên dụng cho từng loại vải. Đối với đồ len, nên sử dụng nước giặt dịu nhẹ.</p>

<h3>4. Cách phơi đúng cách</h3>
<p>Tránh phơi trực tiếp dưới ánh nắng mặt trời gắt, đặc biệt với quần áo màu sẫm để tránh phai màu.</p>',
                'excerpt' => 'Những bí quyết chăm sóc quần áo giúp trang phục luôn bền đẹp và tiết kiệm chi phí',
                'status' => 'published',
                'is_featured' => false,
                'author_id' => 1,
                'blog_category_id' => 3,
                'featured_image' => 'blog/cham-soc-quan-ao.jpg',
                'featured_image_alt' => 'Chăm sóc quần áo',
                'published_at' => Carbon::now()->subDays(15),
                'reading_time' => 6,
                'views_count' => 650,
                'meta_title' => 'Bí quyết chăm sóc quần áo để bền đẹp | Blog Shop Thời Trang',
                'meta_description' => 'Học cách chăm sóc quần áo đúng cách để trang phục luôn bền đẹp. Những mẹo hay từ chuyên gia.',
                'created_at' => Carbon::now()->subDays(15),
                'updated_at' => Carbon::now()->subDays(15),
            ]
        ];

        foreach ($posts as $post) {
            DB::table('blog_posts')->insert($post);
        }

        // Create blog post tags relationships
        DB::table('blog_post_tags')->insert([
            ['blog_post_id' => 1, 'blog_tag_id' => 1, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['blog_post_id' => 1, 'blog_tag_id' => 2, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['blog_post_id' => 2, 'blog_tag_id' => 3, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['blog_post_id' => 2, 'blog_tag_id' => 4, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
            ['blog_post_id' => 3, 'blog_tag_id' => 4, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
        ]);
    }
}
