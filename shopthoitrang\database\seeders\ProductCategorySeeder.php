<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ProductCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Xóa dữ liệu cũ nếu có
        DB::table('product_categories')->truncate();

        // Sử dụng ID thực tế từ database
        DB::table('product_categories')->insert([
            // Áo sơ mi nam (ID: 6) thuộc category "Áo sơ mi nam" và "Thời trang Nam"
            [
                'product_id' => 6,
                'category_id' => 4, // <PERSON>o sơ mi nam
                'is_primary' => true,
                'sort_order' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 6,
                'category_id' => 1, // Thời trang Nam
                'is_primary' => false,
                'sort_order' => 2,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Đầm maxi (ID: 7) thuộc category "Đầm nữ" và "Thời trang Nữ"
            [
                'product_id' => 7,
                'category_id' => 8, // Đầm nữ
                'is_primary' => true,
                'sort_order' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 7,
                'category_id' => 2, // Thời trang Nữ
                'is_primary' => false,
                'sort_order' => 2,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Quần jean nam (ID: 8) thuộc category "Quần jean nam" và "Thời trang Nam"
            [
                'product_id' => 8,
                'category_id' => 7, // Quần jean nam
                'is_primary' => true,
                'sort_order' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 8,
                'category_id' => 1, // Thời trang Nam
                'is_primary' => false,
                'sort_order' => 2,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Áo thun nữ (ID: 9) thuộc category "Áo nữ" và "Thời trang Nữ"
            [
                'product_id' => 9,
                'category_id' => 9, // Áo nữ
                'is_primary' => true,
                'sort_order' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 9,
                'category_id' => 2, // Thời trang Nữ
                'is_primary' => false,
                'sort_order' => 2,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Túi xách (ID: 10) thuộc category "Túi xách" và "Phụ kiện"
            [
                'product_id' => 10,
                'category_id' => 12, // Túi xách
                'is_primary' => true,
                'sort_order' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 10,
                'category_id' => 3, // Phụ kiện
                'is_primary' => false,
                'sort_order' => 2,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ]);
    }
}
