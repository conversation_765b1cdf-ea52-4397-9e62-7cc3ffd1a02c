<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Shipping Zones
        Schema::create('shipping_zones', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->json('countries'); // Array of country codes
            $table->json('states')->nullable(); // Array of state/province codes
            $table->json('cities')->nullable(); // Array of cities
            $table->json('postal_codes')->nullable(); // Array of postal code ranges
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index('is_active');
        });
        
        // Shipping Methods
        Schema::create('shipping_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shipping_zone_id')->constrained('shipping_zones')->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['flat_rate', 'free', 'weight_based', 'price_based', 'calculated']);
            
            // Pricing
            $table->decimal('cost', 12, 2)->default(0);
            $table->decimal('min_order_amount', 12, 2)->nullable(); // Free shipping threshold
            $table->decimal('max_weight', 8, 2)->nullable();
            $table->json('weight_rates')->nullable(); // Weight-based pricing tiers
            $table->json('price_rates')->nullable(); // Price-based pricing tiers
            
            // Delivery time
            $table->integer('min_delivery_days')->nullable();
            $table->integer('max_delivery_days')->nullable();
            
            // Settings
            $table->boolean('is_active')->default(true);
            $table->boolean('requires_address')->default(true);
            $table->integer('sort_order')->default(0);
            
            // Tax settings
            $table->boolean('taxable')->default(false);
            $table->string('tax_class')->nullable();
            
            $table->timestamps();
            
            $table->index(['shipping_zone_id', 'is_active']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_methods');
        Schema::dropIfExists('shipping_zones');
    }
};
