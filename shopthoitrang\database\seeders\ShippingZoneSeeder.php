<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ShippingZoneSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create shipping zones
        $zones = [
            [
                'id' => 1,
                'name' => 'TP. Hồ Chí Minh',
                'countries' => json_encode(['VN']),
                'states' => json_encode(['TP. Hồ Chí Minh']),
                'cities' => json_encode(['TP. Hồ Chí Minh']),
                'postal_codes' => json_encode(['70000-79999']),
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'id' => 2,
                'name' => 'Hà Nội',
                'countries' => json_encode(['VN']),
                'states' => json_encode(['Hà Nội']),
                'cities' => json_encode(['Hà Nội']),
                'postal_codes' => json_encode(['10000-19999']),
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'id' => 3,
                'name' => 'Miền Nam',
                'countries' => json_encode(['VN']),
                'states' => json_encode([
                    'An Giang', 'Bà Rịa - Vũng Tàu', 'Bạc Liêu', 'Bến Tre', 
                    'Bình Dương', 'Bình Phước', 'Bình Thuận', 'Cà Mau', 
                    'Cần Thơ', 'Đồng Nai', 'Đồng Tháp', 'Hậu Giang', 
                    'Kiên Giang', 'Long An', 'Sóc Trăng', 'Tây Ninh', 
                    'Tiền Giang', 'Trà Vinh', 'Vĩnh Long'
                ]),
                'cities' => null,
                'postal_codes' => null,
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'id' => 4,
                'name' => 'Miền Trung',
                'countries' => json_encode(['VN']),
                'states' => json_encode([
                    'Bình Định', 'Đà Nẵng', 'Đắk Lắk', 'Đắk Nông', 
                    'Gia Lai', 'Hà Tĩnh', 'Khánh Hòa', 'Kon Tum', 
                    'Nghệ An', 'Ninh Thuận', 'Phú Yên', 'Quảng Bình', 
                    'Quảng Nam', 'Quảng Ngãi', 'Quảng Trị', 'Thanh Hóa', 
                    'Thừa Thiên Huế'
                ]),
                'cities' => null,
                'postal_codes' => null,
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'id' => 5,
                'name' => 'Miền Bắc',
                'countries' => json_encode(['VN']),
                'states' => json_encode([
                    'Bắc Giang', 'Bắc Kạn', 'Bắc Ninh', 'Cao Bằng', 
                    'Điện Biên', 'Hà Giang', 'Hà Nam', 'Hải Dương', 
                    'Hải Phòng', 'Hòa Bình', 'Hưng Yên', 'Lai Châu', 
                    'Lạng Sơn', 'Lào Cai', 'Nam Định', 'Ninh Bình', 
                    'Phú Thọ', 'Quảng Ninh', 'Sơn La', 'Thái Bình', 
                    'Thái Nguyên', 'Tuyên Quang', 'Vĩnh Phúc', 'Yên Bái'
                ]),
                'cities' => null,
                'postal_codes' => null,
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ];

        foreach ($zones as $zone) {
            DB::table('shipping_zones')->insert($zone);
        }

        // Create shipping methods for each zone
        $shippingMethods = [
            // TP.HCM - Fast delivery
            [
                'shipping_zone_id' => 1,
                'name' => 'Giao hàng nhanh (2-4h)',
                'description' => 'Giao hàng trong ngày tại TP.HCM',
                'type' => 'flat_rate',
                'cost' => 25000,
                'min_delivery_days' => 0,
                'max_delivery_days' => 1,
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'shipping_zone_id' => 1,
                'name' => 'Giao hàng tiêu chuẩn',
                'description' => 'Giao hàng trong 1-2 ngày tại TP.HCM',
                'type' => 'flat_rate',
                'cost' => 15000,
                'min_delivery_days' => 1,
                'max_delivery_days' => 2,
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'shipping_zone_id' => 1,
                'name' => 'Miễn phí giao hàng',
                'description' => 'Miễn phí giao hàng cho đơn từ 500k',
                'type' => 'free',
                'cost' => 0,
                'min_order_amount' => 500000,
                'min_delivery_days' => 1,
                'max_delivery_days' => 3,
                'is_active' => true,
                'sort_order' => 3,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Hà Nội
            [
                'shipping_zone_id' => 2,
                'name' => 'Giao hàng nhanh',
                'description' => 'Giao hàng trong 1-2 ngày tại Hà Nội',
                'type' => 'flat_rate',
                'cost' => 30000,
                'min_delivery_days' => 1,
                'max_delivery_days' => 2,
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'shipping_zone_id' => 2,
                'name' => 'Giao hàng tiêu chuẩn',
                'description' => 'Giao hàng trong 2-3 ngày tại Hà Nội',
                'type' => 'flat_rate',
                'cost' => 20000,
                'min_delivery_days' => 2,
                'max_delivery_days' => 3,
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Miền Nam
            [
                'shipping_zone_id' => 3,
                'name' => 'Giao hàng tiêu chuẩn',
                'description' => 'Giao hàng trong 2-4 ngày',
                'type' => 'flat_rate',
                'cost' => 35000,
                'min_delivery_days' => 2,
                'max_delivery_days' => 4,
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'shipping_zone_id' => 3,
                'name' => 'Miễn phí giao hàng',
                'description' => 'Miễn phí giao hàng cho đơn từ 800k',
                'type' => 'free',
                'cost' => 0,
                'min_order_amount' => 800000,
                'min_delivery_days' => 3,
                'max_delivery_days' => 5,
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Miền Trung
            [
                'shipping_zone_id' => 4,
                'name' => 'Giao hàng tiêu chuẩn',
                'description' => 'Giao hàng trong 3-5 ngày',
                'type' => 'flat_rate',
                'cost' => 40000,
                'min_delivery_days' => 3,
                'max_delivery_days' => 5,
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Miền Bắc
            [
                'shipping_zone_id' => 5,
                'name' => 'Giao hàng tiêu chuẩn',
                'description' => 'Giao hàng trong 3-6 ngày',
                'type' => 'flat_rate',
                'cost' => 45000,
                'min_delivery_days' => 3,
                'max_delivery_days' => 6,
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ];

        foreach ($shippingMethods as $method) {
            DB::table('shipping_methods')->insert($method);
        }
    }
}
