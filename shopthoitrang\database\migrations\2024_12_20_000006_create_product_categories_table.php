<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('new_products')->onDelete('cascade');
            $table->foreignId('category_id')->constrained('new_categories')->onDelete('cascade');
            $table->boolean('is_primary')->default(false); // Primary category for the product
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            // Ensure unique product-category combination
            $table->unique(['product_id', 'category_id']);
            $table->index(['category_id', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_categories');
    }
};
