<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CouponSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('coupons')->insert([
            [
                'code' => 'WELCOME10',
                'name' => 'Chào mừng khách hàng mới',
                'description' => 'Giảm 10% cho đơn hàng đầu tiên',
                'type' => 'percentage',
                'value' => 10.00,
                'minimum_amount' => 200000,
                'maximum_discount' => 100000,
                'usage_limit' => null,
                'usage_limit_per_customer' => 1,
                'used_count' => 0,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMonths(6),
                'first_order_only' => true,
                'is_active' => true,
                'is_public' => true,
                'combine_with_other_coupons' => false,
                'auto_apply' => false,
                'priority' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'code' => 'FREESHIP',
                'name' => 'Miễn phí vận chuyển',
                'description' => 'Miễn phí vận chuyển cho mọi đơn hàng',
                'type' => 'free_shipping',
                'value' => 0,
                'minimum_amount' => 300000,
                'usage_limit' => 1000,
                'usage_limit_per_customer' => 5,
                'used_count' => 0,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMonths(3),
                'is_active' => true,
                'is_public' => true,
                'combine_with_other_coupons' => true,
                'auto_apply' => false,
                'priority' => 2,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'code' => 'SUMMER50',
                'name' => 'Khuyến mãi mùa hè',
                'description' => 'Giảm 50,000đ cho đơn hàng từ 500,000đ',
                'type' => 'fixed',
                'value' => 50000,
                'minimum_amount' => 500000,
                'usage_limit' => 500,
                'usage_limit_per_customer' => 3,
                'used_count' => 0,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addMonths(2),
                'is_active' => true,
                'is_public' => true,
                'combine_with_other_coupons' => false,
                'auto_apply' => false,
                'priority' => 3,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'code' => 'VIP20',
                'name' => 'Ưu đãi khách hàng VIP',
                'description' => 'Giảm 20% cho khách hàng VIP',
                'type' => 'percentage',
                'value' => 20.00,
                'minimum_amount' => 1000000,
                'maximum_discount' => 500000,
                'usage_limit' => null,
                'usage_limit_per_customer' => 10,
                'used_count' => 0,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addYear(),
                'customer_groups' => json_encode(['vip']),
                'is_active' => true,
                'is_public' => false,
                'combine_with_other_coupons' => false,
                'auto_apply' => false,
                'priority' => 4,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'code' => 'FLASH15',
                'name' => 'Flash Sale 15%',
                'description' => 'Giảm 15% trong thời gian giới hạn',
                'type' => 'percentage',
                'value' => 15.00,
                'minimum_amount' => 300000,
                'maximum_discount' => 200000,
                'usage_limit' => 100,
                'usage_limit_per_customer' => 1,
                'used_count' => 0,
                'starts_at' => Carbon::now(),
                'expires_at' => Carbon::now()->addDays(7),
                'is_active' => true,
                'is_public' => true,
                'combine_with_other_coupons' => false,
                'auto_apply' => false,
                'priority' => 5,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'code' => 'AUTOSHIP',
                'name' => 'Tự động miễn phí ship',
                'description' => 'Tự động áp dụng miễn phí ship cho đơn từ 400k',
                'type' => 'free_shipping',
                'value' => 0,
                'minimum_amount' => 400000,
                'usage_limit' => null,
                'usage_limit_per_customer' => null,
                'used_count' => 0,
                'starts_at' => Carbon::now(),
                'expires_at' => null,
                'is_active' => true,
                'is_public' => false,
                'combine_with_other_coupons' => true,
                'auto_apply' => true,
                'priority' => 10,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ]);
    }
}
