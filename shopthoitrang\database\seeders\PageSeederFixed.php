<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PageSeederFixed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pages = [
            [
                'title' => 'Về chúng tôi',
                'slug' => 've-chung-toi',
                'content' => '<h2>Về Shop Thời Trang</h2><p>Shop Thời Trang là cửa hàng thời trang trực tuyến hàng đầu tại Việt Nam.</p>',
                'excerpt' => 'Shop Thời Trang - Cửa hàng thời trang trực tuyến hàng đầu Việt Nam',
                'status' => 'published',
                'template' => 'default',
                'show_in_menu' => true,
                'menu_order' => 1,
                'meta_title' => 'Về chúng tôi - Shop Thời Trang',
                'meta_description' => 'Tìm hiểu về Shop Thời Trang - cửa hàng thời trang trực tuyến hàng đầu Việt Nam.',
                'meta_keywords' => 'về chúng tôi, shop thời trang',
                'canonical_url' => null,
                'og_tags' => null,
                'featured_image' => null,
                'featured_image_alt' => null,
                'author_id' => 1,
                'published_at' => Carbon::now(),
                'parent_id' => null,
                'custom_fields' => null,
                'views_count' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Chính sách bảo mật',
                'slug' => 'chinh-sach-bao-mat',
                'content' => '<h2>Chính sách bảo mật thông tin</h2><p>Chúng tôi cam kết bảo vệ thông tin cá nhân của bạn.</p>',
                'excerpt' => 'Chính sách bảo mật thông tin khách hàng của Shop Thời Trang',
                'status' => 'published',
                'template' => 'default',
                'show_in_menu' => true,
                'menu_order' => 2,
                'meta_title' => 'Chính sách bảo mật - Shop Thời Trang',
                'meta_description' => 'Tìm hiểu về chính sách bảo mật thông tin khách hàng tại Shop Thời Trang.',
                'meta_keywords' => 'chính sách bảo mật, bảo vệ thông tin',
                'canonical_url' => null,
                'og_tags' => null,
                'featured_image' => null,
                'featured_image_alt' => null,
                'author_id' => 1,
                'published_at' => Carbon::now(),
                'parent_id' => null,
                'custom_fields' => null,
                'views_count' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Điều khoản sử dụng',
                'slug' => 'dieu-khoan-su-dung',
                'content' => '<h2>Điều khoản sử dụng</h2><p>Điều khoản và điều kiện sử dụng website Shop Thời Trang.</p>',
                'excerpt' => 'Điều khoản và điều kiện sử dụng website Shop Thời Trang',
                'status' => 'published',
                'template' => 'default',
                'show_in_menu' => true,
                'menu_order' => 3,
                'meta_title' => 'Điều khoản sử dụng - Shop Thời Trang',
                'meta_description' => 'Điều khoản và điều kiện sử dụng website Shop Thời Trang.',
                'meta_keywords' => 'điều khoản sử dụng, quy định',
                'canonical_url' => null,
                'og_tags' => null,
                'featured_image' => null,
                'featured_image_alt' => null,
                'author_id' => 1,
                'published_at' => Carbon::now(),
                'parent_id' => null,
                'custom_fields' => null,
                'views_count' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Chính sách đổi trả',
                'slug' => 'chinh-sach-doi-tra',
                'content' => '<h2>Chính sách đổi trả</h2><p>Chính sách đổi trả sản phẩm tại Shop Thời Trang.</p>',
                'excerpt' => 'Chính sách đổi trả sản phẩm tại Shop Thời Trang',
                'status' => 'published',
                'template' => 'default',
                'show_in_menu' => true,
                'menu_order' => 4,
                'meta_title' => 'Chính sách đổi trả - Shop Thời Trang',
                'meta_description' => 'Tìm hiểu về chính sách đổi trả sản phẩm tại Shop Thời Trang.',
                'meta_keywords' => 'chính sách đổi trả, hoàn tiền',
                'canonical_url' => null,
                'og_tags' => null,
                'featured_image' => null,
                'featured_image_alt' => null,
                'author_id' => 1,
                'published_at' => Carbon::now(),
                'parent_id' => null,
                'custom_fields' => null,
                'views_count' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'title' => 'Liên hệ',
                'slug' => 'lien-he',
                'content' => '<h2>Liên hệ với chúng tôi</h2><p>Thông tin liên hệ với Shop Thời Trang.</p>',
                'excerpt' => 'Thông tin liên hệ với Shop Thời Trang',
                'status' => 'published',
                'template' => 'contact',
                'show_in_menu' => true,
                'menu_order' => 5,
                'meta_title' => 'Liên hệ - Shop Thời Trang',
                'meta_description' => 'Liên hệ với Shop Thời Trang qua điện thoại, email hoặc đến trực tiếp cửa hàng.',
                'meta_keywords' => 'liên hệ, thông tin liên hệ',
                'canonical_url' => null,
                'og_tags' => null,
                'featured_image' => null,
                'featured_image_alt' => null,
                'author_id' => 1,
                'published_at' => Carbon::now(),
                'parent_id' => null,
                'custom_fields' => null,
                'views_count' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ];

        foreach ($pages as $page) {
            DB::table('pages')->insert($page);
        }
    }
}
