<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SimpleTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Kiểm tra products
        $products = DB::table('new_products')->select('id', 'name')->get();
        echo "Products found:\n";
        foreach ($products as $product) {
            echo "ID: {$product->id}, Name: {$product->name}\n";
        }
        
        // Kiểm tra categories
        $categories = DB::table('new_categories')->select('id', 'name')->get();
        echo "\nCategories found:\n";
        foreach ($categories as $category) {
            echo "ID: {$category->id}, Name: {$category->name}\n";
        }
        
        // Thử insert product_categories với ID thực tế
        if ($products->count() > 0 && $categories->count() > 0) {
            $firstProduct = $products->first();
            $firstCategory = $categories->first();
            
            try {
                DB::table('product_categories')->insert([
                    'product_id' => $firstProduct->id,
                    'category_id' => $firstCategory->id,
                    'is_primary' => true,
                    'sort_order' => 1,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);
                echo "\nSuccessfully inserted product_category relationship\n";
            } catch (\Exception $e) {
                echo "\nError inserting product_category: " . $e->getMessage() . "\n";
            }
        }
    }
}
