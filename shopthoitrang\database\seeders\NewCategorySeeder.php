<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class NewCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Parent categories
        $parentCategories = [
            [
                'id' => 1,
                'name' => 'Thời trang Nam',
                'slug' => 'thoi-trang-nam',
                'description' => 'Bộ sưu tập thời trang dành cho nam giới với đa dạng phong cách từ công sở đến casual.',
                'short_description' => 'Thời trang nam đa dạng phong cách',
                'parent_id' => null,
                'sort_order' => 1,
                'level' => 0,
                'image' => 'categories/thoi-trang-nam.jpg',
                'is_active' => true,
                'is_featured' => true,
                'show_in_menu' => true,
                'meta_title' => 'Thời trang Nam - Shop Thời Trang',
                'meta_description' => 'Khám phá bộ sưu tập thời trang nam đa dạng với áo sơ mi, quần tây, áo thun và nhiều sản phẩm khác.',
                'meta_keywords' => 'thời trang nam, áo nam, quần nam, phụ kiện nam',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'id' => 2,
                'name' => 'Thời trang Nữ',
                'slug' => 'thoi-trang-nu',
                'description' => 'Bộ sưu tập thời trang nữ với những thiết kế tinh tế, nữ tính và hiện đại.',
                'short_description' => 'Thời trang nữ tinh tế và hiện đại',
                'parent_id' => null,
                'sort_order' => 2,
                'level' => 0,
                'image' => 'categories/thoi-trang-nu.jpg',
                'is_active' => true,
                'is_featured' => true,
                'show_in_menu' => true,
                'meta_title' => 'Thời trang Nữ - Shop Thời Trang',
                'meta_description' => 'Bộ sưu tập thời trang nữ với đầm, áo, quần và phụ kiện thời trang cao cấp.',
                'meta_keywords' => 'thời trang nữ, đầm nữ, áo nữ, quần nữ, phụ kiện nữ',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'id' => 3,
                'name' => 'Phụ kiện',
                'slug' => 'phu-kien',
                'description' => 'Các phụ kiện thời trang như túi xách, giày dép, trang sức và nhiều hơn nữa.',
                'short_description' => 'Phụ kiện thời trang đa dạng',
                'parent_id' => null,
                'sort_order' => 3,
                'level' => 0,
                'image' => 'categories/phu-kien.jpg',
                'is_active' => true,
                'is_featured' => true,
                'show_in_menu' => true,
                'meta_title' => 'Phụ kiện thời trang - Shop Thời Trang',
                'meta_description' => 'Bộ sưu tập phụ kiện thời trang với túi xách, giày dép, trang sức và nhiều sản phẩm khác.',
                'meta_keywords' => 'phụ kiện, túi xách, giày dép, trang sức',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ];

        // Child categories for Men's Fashion
        $childCategories = [
            // Men's subcategories
            [
                'name' => 'Áo sơ mi nam',
                'slug' => 'ao-so-mi-nam',
                'description' => 'Áo sơ mi nam công sở và casual với nhiều kiểu dáng và màu sắc.',
                'parent_id' => 1,
                'sort_order' => 1,
                'level' => 1,
                'image' => 'categories/ao-so-mi-nam.jpg',
                'is_active' => true,
                'meta_title' => 'Áo sơ mi nam - Thời trang công sở',
                'meta_description' => 'Bộ sưu tập áo sơ mi nam công sở và casual chất lượng cao.',
            ],
            [
                'name' => 'Áo thun nam',
                'slug' => 'ao-thun-nam',
                'description' => 'Áo thun nam với chất liệu cotton thoáng mát và thiết kế đa dạng.',
                'parent_id' => 1,
                'sort_order' => 2,
                'level' => 1,
                'image' => 'categories/ao-thun-nam.jpg',
                'is_active' => true,
                'meta_title' => 'Áo thun nam - Thời trang casual',
                'meta_description' => 'Áo thun nam chất lượng với nhiều màu sắc và kiểu dáng.',
            ],
            [
                'name' => 'Quần tây nam',
                'slug' => 'quan-tay-nam',
                'description' => 'Quần tây nam công sở với form dáng chuẩn và chất liệu cao cấp.',
                'parent_id' => 1,
                'sort_order' => 3,
                'level' => 1,
                'image' => 'categories/quan-tay-nam.jpg',
                'is_active' => true,
                'meta_title' => 'Quần tây nam - Thời trang công sở',
                'meta_description' => 'Quần tây nam công sở với chất liệu cao cấp và form dáng chuẩn.',
            ],
            [
                'name' => 'Quần jean nam',
                'slug' => 'quan-jean-nam',
                'description' => 'Quần jean nam với nhiều kiểu dáng từ slim fit đến regular fit.',
                'parent_id' => 1,
                'sort_order' => 4,
                'level' => 1,
                'image' => 'categories/quan-jean-nam.jpg',
                'is_active' => true,
                'meta_title' => 'Quần jean nam - Thời trang casual',
                'meta_description' => 'Quần jean nam với nhiều kiểu dáng và màu sắc thời trang.',
            ],
            
            // Women's subcategories
            [
                'name' => 'Đầm nữ',
                'slug' => 'dam-nu',
                'description' => 'Bộ sưu tập đầm nữ với thiết kế nữ tính và thanh lịch.',
                'parent_id' => 2,
                'sort_order' => 1,
                'level' => 1,
                'image' => 'categories/dam-nu.jpg',
                'is_active' => true,
                'meta_title' => 'Đầm nữ - Thời trang nữ tính',
                'meta_description' => 'Bộ sưu tập đầm nữ với thiết kế đa dạng từ công sở đến dạo phố.',
            ],
            [
                'name' => 'Áo nữ',
                'slug' => 'ao-nu',
                'description' => 'Áo nữ với nhiều kiểu dáng từ áo sơ mi, áo thun đến áo khoác.',
                'parent_id' => 2,
                'sort_order' => 2,
                'level' => 1,
                'image' => 'categories/ao-nu.jpg',
                'is_active' => true,
                'meta_title' => 'Áo nữ - Thời trang đa dạng',
                'meta_description' => 'Bộ sưu tập áo nữ với nhiều kiểu dáng và phong cách.',
            ],
            [
                'name' => 'Quần nữ',
                'slug' => 'quan-nu',
                'description' => 'Quần nữ với thiết kế hiện đại và thoải mái.',
                'parent_id' => 2,
                'sort_order' => 3,
                'level' => 1,
                'image' => 'categories/quan-nu.jpg',
                'is_active' => true,
                'meta_title' => 'Quần nữ - Thời trang hiện đại',
                'meta_description' => 'Bộ sưu tập quần nữ với thiết kế hiện đại và chất liệu cao cấp.',
            ],
            [
                'name' => 'Chân váy',
                'slug' => 'chan-vay',
                'description' => 'Chân váy với nhiều kiểu dáng từ ngắn đến dài, phù hợp mọi dịp.',
                'parent_id' => 2,
                'sort_order' => 4,
                'level' => 1,
                'image' => 'categories/chan-vay.jpg',
                'is_active' => true,
                'meta_title' => 'Chân váy - Thời trang nữ tính',
                'meta_description' => 'Bộ sưu tập chân váy với nhiều kiểu dáng thời trang.',
            ],
            
            // Accessories subcategories
            [
                'name' => 'Túi xách',
                'slug' => 'tui-xach',
                'description' => 'Túi xách với nhiều kiểu dáng và kích thước phù hợp mọi nhu cầu.',
                'parent_id' => 3,
                'sort_order' => 1,
                'level' => 1,
                'image' => 'categories/tui-xach.jpg',
                'is_active' => true,
                'meta_title' => 'Túi xách - Phụ kiện thời trang',
                'meta_description' => 'Bộ sưu tập túi xách với thiết kế đa dạng và chất lượng cao.',
            ],
            [
                'name' => 'Giày dép',
                'slug' => 'giay-dep',
                'description' => 'Giày dép thời trang cho nam và nữ với nhiều phong cách.',
                'parent_id' => 3,
                'sort_order' => 2,
                'level' => 1,
                'image' => 'categories/giay-dep.jpg',
                'is_active' => true,
                'meta_title' => 'Giày dép - Phụ kiện thời trang',
                'meta_description' => 'Bộ sưu tập giày dép thời trang với nhiều kiểu dáng.',
            ]
        ];

        // Insert parent categories first
        foreach ($parentCategories as $category) {
            DB::table('new_categories')->insert($category);
        }

        // Insert child categories
        foreach ($childCategories as $category) {
            $category['created_at'] = Carbon::now();
            $category['updated_at'] = Carbon::now();
            $category['is_featured'] = false;
            $category['show_in_menu'] = true;
            $category['is_active'] = true;
            
            DB::table('new_categories')->insert($category);
        }
    }
}
