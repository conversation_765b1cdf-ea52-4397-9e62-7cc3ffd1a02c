<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ProductTagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('product_tags')->insert([
            // <PERSON><PERSON> sơ mi nam - Tags: <PERSON><PERSON><PERSON>h<PERSON>, <PERSON>h<PERSON><PERSON> trang công sở, <PERSON> cấ<PERSON>
            [
                'product_id' => 1,
                'tag_id' => 1, // Mới nhất
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 1,
                'tag_id' => 5, // Thời trang công sở
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 1,
                'tag_id' => 4, // <PERSON> cấp
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Đầm maxi - Tags: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> nhấ<PERSON>
            [
                'product_id' => 2,
                'tag_id' => 2, // <PERSON><PERSON> chạy
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 2,
                'tag_id' => 6, // Casual
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 2,
                'tag_id' => 1, // Mới nhất
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Quần jean nam - Tags: Giảm giá, Casual, Bán chạy
            [
                'product_id' => 3,
                'tag_id' => 3, // Giảm giá
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 3,
                'tag_id' => 6, // Casual
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 3,
                'tag_id' => 2, // Bán chạy
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Áo thun nữ - Tags: Casual, Giảm giá
            [
                'product_id' => 4,
                'tag_id' => 6, // Casual
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 4,
                'tag_id' => 3, // Giảm giá
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Túi xách - Tags: Cao cấp, Mới nhất, Bán chạy
            [
                'product_id' => 5,
                'tag_id' => 4, // Cao cấp
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 5,
                'tag_id' => 1, // Mới nhất
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 5,
                'tag_id' => 2, // Bán chạy
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ]);

        // Update products count for tags
        DB::statement('
            UPDATE tags 
            SET products_count = (
                SELECT COUNT(*) 
                FROM product_tags 
                WHERE product_tags.tag_id = tags.id
            )
        ');
    }
}
