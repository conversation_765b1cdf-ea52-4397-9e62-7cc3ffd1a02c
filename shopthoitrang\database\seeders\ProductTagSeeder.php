<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ProductTagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('product_tags')->insert([
            // <PERSON><PERSON> sơ mi nam (ID: 6) - Tags: <PERSON><PERSON><PERSON>h<PERSON>, <PERSON>h<PERSON><PERSON> trang công sở, <PERSON> c<PERSON>
            [
                'product_id' => 6,
                'tag_id' => 1, // M<PERSON>i nhất
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 6,
                'tag_id' => 5, // Thời trang công sở
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 6,
                'tag_id' => 4, // <PERSON> cấ<PERSON>
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // <PERSON><PERSON><PERSON> maxi (ID: 7) - Tags: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> nhất
            [
                'product_id' => 7,
                'tag_id' => 2, // <PERSON><PERSON> chạy
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 7,
                'tag_id' => 6, // Casual
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 7,
                'tag_id' => 1, // Mới nhất
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Quần jean nam (ID: 8) - Tags: Giảm giá, Casual, Bán chạy
            [
                'product_id' => 8,
                'tag_id' => 3, // Giảm giá
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 8,
                'tag_id' => 6, // Casual
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 8,
                'tag_id' => 2, // Bán chạy
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Áo thun nữ (ID: 9) - Tags: Casual, Giảm giá
            [
                'product_id' => 9,
                'tag_id' => 6, // Casual
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 9,
                'tag_id' => 3, // Giảm giá
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],

            // Túi xách (ID: 10) - Tags: Cao cấp, Mới nhất, Bán chạy
            [
                'product_id' => 10,
                'tag_id' => 4, // Cao cấp
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 10,
                'tag_id' => 1, // Mới nhất
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'product_id' => 10,
                'tag_id' => 2, // Bán chạy
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ]);

        // Update products count for tags
        DB::statement('
            UPDATE tags 
            SET products_count = (
                SELECT COUNT(*) 
                FROM product_tags 
                WHERE product_tags.tag_id = tags.id
            )
        ');
    }
}
