<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('payment_methods')->insert([
            [
                'name' => 'Thanh toán khi nhận hàng (COD)',
                'code' => 'COD',
                'description' => 'Thanh toán bằng tiền mặt khi nhận hàng',
                'icon' => 'payment-icons/cod.png',
                'gateway' => null,
                'gateway_config' => null,
                'is_active' => true,
                'available_countries' => json_encode(['VN']),
                'min_amount' => 0,
                'max_amount' => 10000000,
                'fixed_fee' => 0,
                'percentage_fee' => 0,
                'sort_order' => 1,
                'instructions' => 'Quý khách vui lòng chuẩn bị đủ tiền mặt khi nhận hàng.',
                'supports_refunds' => false,
                'supports_partial_refunds' => false,
                'requires_confirmation' => false,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Chuyển khoản ngân hàng',
                'code' => 'BANK_TRANSFER',
                'description' => 'Chuyển khoản qua ngân hàng',
                'icon' => 'payment-icons/bank-transfer.png',
                'gateway' => null,
                'gateway_config' => json_encode([
                    'bank_name' => 'Vietcombank',
                    'account_number' => '**********',
                    'account_name' => 'SHOP THOI TRANG',
                    'branch' => 'Chi nhánh TP.HCM'
                ]),
                'is_active' => true,
                'available_countries' => json_encode(['VN']),
                'min_amount' => 100000,
                'max_amount' => null,
                'fixed_fee' => 0,
                'percentage_fee' => 0,
                'sort_order' => 2,
                'instructions' => 'Vui lòng chuyển khoản theo thông tin bên dưới và gửi ảnh chụp biên lai cho chúng tôi.',
                'supports_refunds' => true,
                'supports_partial_refunds' => true,
                'requires_confirmation' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'VNPay',
                'code' => 'VNPAY',
                'description' => 'Thanh toán qua VNPay (ATM, Visa, MasterCard)',
                'icon' => 'payment-icons/vnpay.png',
                'gateway' => 'vnpay',
                'gateway_config' => json_encode([
                    'merchant_id' => '',
                    'secret_key' => '',
                    'endpoint' => 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html'
                ]),
                'is_active' => true,
                'available_countries' => json_encode(['VN']),
                'min_amount' => 10000,
                'max_amount' => 500000000,
                'fixed_fee' => 0,
                'percentage_fee' => 0.025, // 2.5%
                'sort_order' => 3,
                'instructions' => 'Thanh toán an toàn qua cổng VNPay với thẻ ATM, Visa, MasterCard.',
                'supports_refunds' => true,
                'supports_partial_refunds' => false,
                'requires_confirmation' => false,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'MoMo',
                'code' => 'MOMO',
                'description' => 'Thanh toán qua ví điện tử MoMo',
                'icon' => 'payment-icons/momo.png',
                'gateway' => 'momo',
                'gateway_config' => json_encode([
                    'partner_code' => '',
                    'access_key' => '',
                    'secret_key' => '',
                    'endpoint' => 'https://test-payment.momo.vn/v2/gateway/api/create'
                ]),
                'is_active' => true,
                'available_countries' => json_encode(['VN']),
                'min_amount' => 10000,
                'max_amount' => 50000000,
                'fixed_fee' => 0,
                'percentage_fee' => 0.02, // 2%
                'sort_order' => 4,
                'instructions' => 'Thanh toán nhanh chóng và an toàn qua ví MoMo.',
                'supports_refunds' => true,
                'supports_partial_refunds' => false,
                'requires_confirmation' => false,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'ZaloPay',
                'code' => 'ZALOPAY',
                'description' => 'Thanh toán qua ví điện tử ZaloPay',
                'icon' => 'payment-icons/zalopay.png',
                'gateway' => 'zalopay',
                'gateway_config' => json_encode([
                    'app_id' => '',
                    'key1' => '',
                    'key2' => '',
                    'endpoint' => 'https://sb-openapi.zalopay.vn/v2/create'
                ]),
                'is_active' => false, // Disabled by default
                'available_countries' => json_encode(['VN']),
                'min_amount' => 1000,
                'max_amount' => 50000000,
                'fixed_fee' => 0,
                'percentage_fee' => 0.015, // 1.5%
                'sort_order' => 5,
                'instructions' => 'Thanh toán tiện lợi qua ví ZaloPay.',
                'supports_refunds' => true,
                'supports_partial_refunds' => false,
                'requires_confirmation' => false,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ]);
    }
}
