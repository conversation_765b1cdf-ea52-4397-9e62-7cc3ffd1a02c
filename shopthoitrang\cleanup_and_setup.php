<?php

/**
 * Script cleanup và setup database mới
 * Xử lý các bảng có thể bị conflict với database cũ
 */

echo "🧹 Cleanup và Setup Database Mới...\n\n";

// <PERSON>h sách bảng có thể bị conflict
$conflictTables = [
    'carts',
    'categories', 
    'products',
    'users',
    'orders',
    'order_items',
    'reviews',
    'coupons',
    'pages',
    'settings'
];

// Danh sách migration files theo thứ tự
$migrations = [
    '2024_12_20_000001_create_new_users_table.php',
    '2024_12_20_000002_create_new_categories_table.php', 
    '2024_12_20_000003_create_brands_table.php',
    '2024_12_20_000004_create_new_products_table.php',
    '2024_12_20_000005_create_product_variants_table.php',
    '2024_12_20_000006_create_product_categories_table.php',
    '2024_12_20_000007_create_new_orders_table.php',
    '2024_12_20_000008_create_order_items_table.php',
    '2024_12_20_000009_create_carts_table.php',
    '2024_12_20_000010_create_reviews_table.php',
    '2024_12_20_000011_create_coupons_table.php',
    '2024_12_20_000012_create_wishlists_table.php',
    '2024_12_20_000013_create_product_tags_table.php',
    '2024_12_20_000014_create_shipping_zones_table.php',
    '2024_12_20_000015_create_payment_methods_table.php',
    '2024_12_20_000016_create_pages_table.php',
    '2024_12_20_000017_create_blogs_table.php',
    '2024_12_20_000018_create_seo_redirects_table.php',
    '2024_12_20_000019_create_settings_table.php',
    '2024_12_20_000020_create_banners_newsletters_table.php',
];

// Danh sách seeders theo thứ tự
$seeders = [
    'NewUserSeeder',
    'BrandSeeder', 
    'NewCategorySeeder',
    'TagSeeder',
    'NewProductSeeder',
    'ProductCategorySeeder',
    'ProductTagSeeder',
    'ProductVariantSeeder',
    'CouponSeeder',
    'ShippingZoneSeeder',
    'PaymentMethodSeeder',
    'PageSeeder',
    'BlogSeeder',
    'SettingSeeder',
    'BannerSeeder',
    'NewsletterSeeder',
    'SeoMetaSeeder',
];

function runCommand($command, $showOutput = false) {
    echo "📋 Chạy: $command\n";
    $output = [];
    $return_var = 0;
    exec($command . ' 2>&1', $output, $return_var);
    
    if ($return_var === 0) {
        echo "✅ Thành công!\n";
        if ($showOutput && !empty($output)) {
            foreach ($output as $line) {
                echo "   $line\n";
            }
        }
        return true;
    } else {
        echo "❌ Lỗi:\n";
        foreach ($output as $line) {
            echo "   $line\n";
        }
        return false;
    }
}

function backupOldData() {
    echo "💾 Backup dữ liệu cũ...\n";
    
    $backupTables = ['users', 'categories', 'products', 'orders'];
    $timestamp = date('Y_m_d_H_i_s');
    
    foreach ($backupTables as $table) {
        $command = "php artisan tinker --execute=\"
            if (Schema::hasTable('$table')) {
                DB::statement('CREATE TABLE {$table}_backup_$timestamp AS SELECT * FROM $table');
                echo 'Backed up $table to {$table}_backup_$timestamp';
            }
        \"";
        
        echo "📦 Backup bảng: $table\n";
        runCommand($command);
    }
    
    echo "✅ Backup hoàn tất!\n\n";
}

function cleanupConflictTables($tables) {
    echo "🧹 Cleanup các bảng có thể conflict...\n";
    
    foreach ($tables as $table) {
        $command = "php artisan tinker --execute=\"
            if (Schema::hasTable('$table')) {
                Schema::dropIfExists('$table');
                echo 'Dropped table: $table';
            } else {
                echo 'Table $table does not exist';
            }
        \"";
        
        echo "🗑️  Xóa bảng: $table\n";
        runCommand($command);
    }
    
    echo "✅ Cleanup hoàn tất!\n\n";
}

function runMigrations($migrations) {
    echo "📦 Chạy migrations...\n";
    
    foreach ($migrations as $migration) {
        $command = "php artisan migrate --path=/database/migrations/$migration --force";
        if (!runCommand($command)) {
            echo "❌ Migration thất bại: $migration\n";
            return false;
        }
    }
    
    echo "✅ Tất cả migrations đã chạy thành công!\n\n";
    return true;
}

function runSeeders($seeders) {
    echo "🌱 Chạy seeders...\n";
    
    foreach ($seeders as $seeder) {
        $command = "php artisan db:seed --class=$seeder --force";
        if (!runCommand($command)) {
            echo "❌ Seeder thất bại: $seeder\n";
            echo "ℹ️  Tiếp tục với seeder tiếp theo...\n";
            // Không return false để tiếp tục với các seeder khác
        }
    }
    
    echo "✅ Seeders đã chạy xong!\n\n";
    return true;
}

function verifyNewDatabase() {
    echo "🔍 Kiểm tra database mới...\n";
    
    $newTables = [
        'new_users' => 'Users mới',
        'brands' => 'Brands',
        'new_categories' => 'Categories mới', 
        'new_products' => 'Products mới',
        'product_variants' => 'Product Variants',
        'coupons' => 'Coupons',
        'settings' => 'Settings',
        'banners' => 'Banners',
        'pages' => 'Pages',
        'blog_posts' => 'Blog Posts'
    ];
    
    foreach ($newTables as $table => $name) {
        $command = "php artisan tinker --execute=\"
            if (Schema::hasTable('$table')) {
                \$count = DB::table('$table')->count();
                echo '$name: ' . \$count . ' records';
            } else {
                echo '$name: Table not found';
            }
        \"";
        
        runCommand($command, true);
    }
    
    echo "✅ Kiểm tra database hoàn tất!\n\n";
}

function showFinalSummary() {
    echo "🎉 SETUP HOÀN TẤT!\n\n";
    echo "📊 Database mới đã được tạo với các bảng:\n";
    echo "   👤 new_users - Quản lý người dùng nâng cao\n";
    echo "   🏷️  new_categories - Danh mục có cấu trúc cây\n";
    echo "   🛍️  new_products - Sản phẩm với SEO\n";
    echo "   🎨 product_variants - Biến thể sản phẩm\n";
    echo "   🏢 brands - Thương hiệu\n";
    echo "   📦 new_orders - Đơn hàng chi tiết\n";
    echo "   🎫 coupons - Mã giảm giá\n";
    echo "   ⚙️  settings - Cấu hình hệ thống\n";
    echo "   📝 pages - Trang tĩnh\n";
    echo "   📰 blog_posts - Bài viết blog\n";
    echo "   🎯 seo_meta - SEO metadata\n\n";
    
    echo "🔑 Tài khoản đăng nhập:\n";
    echo "   👤 Admin: <EMAIL> / admin123\n";
    echo "   👤 Customer: <EMAIL> / 123456\n\n";
    
    echo "🎁 Mã giảm giá có sẵn:\n";
    echo "   WELCOME10, FREESHIP, SUMMER50, VIP20, FLASH15\n\n";
    
    echo "📚 Bước tiếp theo:\n";
    echo "   1. Tạo Models cho các bảng mới\n";
    echo "   2. Cập nhật Controllers\n";
    echo "   3. Thiết kế giao diện\n";
    echo "   4. Tích hợp SEO features\n\n";
    
    echo "✨ Database mới đã sẵn sàng sử dụng!\n";
}

// Main execution
try {
    echo "⚠️  CẢNH BÁO: Script này sẽ:\n";
    echo "   1. Backup dữ liệu cũ\n";
    echo "   2. Xóa các bảng có thể conflict\n";
    echo "   3. Tạo database mới hoàn toàn\n\n";
    echo "Bạn có chắc chắn muốn tiếp tục? (y/N): ";
    
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    fclose($handle);
    
    if (trim(strtolower($line)) !== 'y') {
        echo "❌ Đã hủy bỏ.\n";
        exit(0);
    }
    
    // Backup dữ liệu cũ
    backupOldData();
    
    // Cleanup các bảng conflict
    cleanupConflictTables($conflictTables);
    
    // Chạy migrations
    if (!runMigrations($migrations)) {
        echo "❌ Setup thất bại tại bước migration.\n";
        exit(1);
    }
    
    // Chạy seeders
    runSeeders($seeders);
    
    // Kiểm tra database mới
    verifyNewDatabase();
    
    // Hiển thị tóm tắt
    showFinalSummary();
    
} catch (Exception $e) {
    echo "❌ Lỗi: " . $e->getMessage() . "\n";
    exit(1);
}
