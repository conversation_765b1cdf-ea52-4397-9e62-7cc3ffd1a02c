<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Banners/Sliders
        Schema::create('banners', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('image');
            $table->string('mobile_image')->nullable(); // Responsive image for mobile
            $table->string('link_url')->nullable();
            $table->string('link_text')->nullable();
            $table->boolean('open_in_new_tab')->default(false);
            
            // Display settings
            $table->enum('type', ['slider', 'banner', 'popup', 'notification'])->default('banner');
            $table->string('position')->default('home_slider'); // home_slider, sidebar, header, footer, etc.
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            
            // Scheduling
            $table->timestamp('starts_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            
            // Targeting
            $table->json('target_pages')->nullable(); // Specific pages to show on
            $table->json('target_categories')->nullable(); // Product categories
            $table->json('target_user_groups')->nullable(); // Customer segments
            
            // Analytics
            $table->integer('views_count')->default(0);
            $table->integer('clicks_count')->default(0);
            
            $table->timestamps();
            
            $table->index(['type', 'position', 'is_active']);
            $table->index(['is_active', 'starts_at', 'expires_at']);
            $table->index('sort_order');
        });
        
        // Newsletter Subscribers
        Schema::create('newsletter_subscribers', function (Blueprint $table) {
            $table->id();
            $table->string('email')->unique();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->enum('status', ['subscribed', 'unsubscribed', 'pending'])->default('pending');
            $table->timestamp('subscribed_at')->nullable();
            $table->timestamp('unsubscribed_at')->nullable();
            $table->string('subscription_source')->nullable(); // popup, footer, checkout, etc.
            $table->json('preferences')->nullable(); // Email preferences
            $table->string('unsubscribe_token')->nullable();
            $table->timestamps();
            
            $table->index(['email', 'status']);
            $table->index('status');
        });
        
        // Newsletter Campaigns
        Schema::create('newsletter_campaigns', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('subject');
            $table->longText('content');
            $table->string('template')->default('default');
            
            // Campaign settings
            $table->enum('status', ['draft', 'scheduled', 'sending', 'sent', 'cancelled'])->default('draft');
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('sent_at')->nullable();
            
            // Targeting
            $table->json('recipient_filters')->nullable(); // Subscriber segments
            $table->integer('total_recipients')->default(0);
            
            // Analytics
            $table->integer('sent_count')->default(0);
            $table->integer('delivered_count')->default(0);
            $table->integer('opened_count')->default(0);
            $table->integer('clicked_count')->default(0);
            $table->integer('bounced_count')->default(0);
            $table->integer('unsubscribed_count')->default(0);
            
            $table->timestamps();
            
            $table->index(['status', 'scheduled_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('newsletter_campaigns');
        Schema::dropIfExists('newsletter_subscribers');
        Schema::dropIfExists('banners');
    }
};
