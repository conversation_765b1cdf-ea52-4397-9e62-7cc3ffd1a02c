<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('new_products')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('new_users')->onDelete('cascade');
            $table->foreignId('order_id')->nullable()->constrained('new_orders')->onDelete('set null');
            $table->foreignId('order_item_id')->nullable()->constrained('order_items')->onDelete('set null');
            
            // Review content
            $table->string('title')->nullable();
            $table->text('content');
            $table->integer('rating'); // 1-5 stars
            
            // Review metadata
            $table->boolean('is_verified_purchase')->default(false);
            $table->boolean('is_approved')->default(false);
            $table->boolean('is_featured')->default(false);
            
            // Review media
            $table->json('images')->nullable(); // Review images
            $table->json('videos')->nullable(); // Review videos
            
            // Helpful votes
            $table->integer('helpful_count')->default(0);
            $table->integer('not_helpful_count')->default(0);
            
            // Admin response
            $table->text('admin_response')->nullable();
            $table->timestamp('admin_response_at')->nullable();
            
            // Review attributes (for specific product types)
            $table->json('attributes_rating')->nullable(); // Quality, Fit, Value, etc.
            
            $table->timestamps();
            
            // Indexes
            $table->index(['product_id', 'is_approved', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['rating', 'is_approved']);
            $table->index('is_verified_purchase');
            
            // Ensure one review per user per product
            $table->unique(['product_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews');
    }
};
