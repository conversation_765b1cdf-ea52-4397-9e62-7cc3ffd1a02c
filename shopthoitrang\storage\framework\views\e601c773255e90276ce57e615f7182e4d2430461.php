<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><PERSON><PERSON><PERSON>ý - Shop Thời Trang Nhật Bản</title>
    <meta name="description" content="Tạo tài khoản mới tại Shop Thời Trang để trải nghiệm mua sắm tuyệt vời với phong cách <PERSON>h<PERSON> Bản">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Japanese Theme CSS -->
    <link type="text/css" rel="stylesheet" href="<?php echo e(asset('css/japanese-theme.css')); ?>" />
</head>

<body class="auth-body-japanese">
    <div class="auth-container-japanese">
        <div class="auth-wrapper">
            <!-- Left Side - Branding -->
            <div class="auth-branding">
                <div class="branding-content">
                    <div class="logo-section">
                        <a href="<?php echo e(route('home.index')); ?>" class="brand-logo">
                            <img src="<?php echo e(asset('img/logo.png')); ?>" alt="Shop Thời Trang" class="logo-img">
                            <span class="brand-text">Shop Thời Trang</span>
                        </a>
                    </div>
                    <div class="branding-text">
                        <h2 class="welcome-title">Tham Gia Cùng Chúng Tôi</h2>
                        <p class="welcome-subtitle">Tạo tài khoản để khám phá thế giới thời trang Nhật Bản đầy tinh tế</p>
                    </div>
                    <div class="branding-features">
                        <div class="feature-item">
                            <i class="fas fa-gift"></i>
                            <span>Ưu đãi thành viên</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-star"></i>
                            <span>Sản phẩm độc quyền</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-heart"></i>
                            <span>Wishlist cá nhân</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Register Form -->
            <div class="auth-form-section">
                <div class="form-container">
                    <div class="form-header">
                        <h1 class="form-title">Đăng Ký</h1>
                        <p class="form-subtitle">Điền thông tin để tạo tài khoản mới</p>
                    </div>

                    <!-- Alert Messages -->
                    <?php if(Session::has('message')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo e(Session::get('message')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <?php Session::put('message', null); ?>
                    <?php endif; ?>

                    <form action="<?php echo e(route('user.cus_register')); ?>" method="post" id="registerForm" class="auth-form">
                        <?php echo csrf_field(); ?>

                        <!-- Name Field -->
                        <div class="form-group">
                            <label for="name" class="form-label">
                                <i class="fas fa-user me-2"></i>
                                Họ và tên
                            </label>
                            <input
                                type="text"
                                name="name"
                                id="name"
                                class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                placeholder="Nhập họ và tên đầy đủ"
                                value="<?php echo e(old('name')); ?>"
                                required
                                autocomplete="name">
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    <?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Email Field -->
                        <div class="form-group">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>
                                Email
                            </label>
                            <input
                                type="email"
                                name="email"
                                id="email"
                                class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                placeholder="Nhập địa chỉ email"
                                value="<?php echo e(old('email')); ?>"
                                required
                                autocomplete="email">
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    <?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Password Field -->
                        <div class="form-group">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>
                                Mật khẩu
                            </label>
                            <div class="password-input-wrapper">
                                <input
                                    type="password"
                                    name="password"
                                    id="password"
                                    class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    placeholder="Nhập mật khẩu (tối thiểu 8 ký tự)"
                                    required
                                    autocomplete="new-password"
                                    minlength="8">
                                <button type="button" class="password-toggle" onclick="togglePassword('password', 'passwordToggleIcon')">
                                    <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                </button>
                            </div>
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    <?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Phone Field -->
                        <div class="form-group">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-2"></i>
                                Số điện thoại
                            </label>
                            <input
                                type="tel"
                                name="phone"
                                id="phone"
                                class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                placeholder="Nhập số điện thoại"
                                value="<?php echo e(old('phone')); ?>"
                                required
                                autocomplete="tel">
                            <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    <?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Address Field -->
                        <div class="form-group">
                            <label for="address" class="form-label">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                Địa chỉ
                            </label>
                            <input
                                type="text"
                                name="address"
                                id="address"
                                class="form-control <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                placeholder="Nhập địa chỉ của bạn"
                                value="<?php echo e(old('address')); ?>"
                                required
                                autocomplete="address-line1">
                            <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    <?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms">
                                    Tôi đồng ý với <a href="#" class="terms-link">Điều khoản sử dụng</a> và <a href="#" class="terms-link">Chính sách bảo mật</a>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="btn-japanese btn-primary btn-register">
                            <i class="fas fa-user-plus me-2"></i>
                            Tạo Tài Khoản
                        </button>

                        <!-- Login Link -->
                        <div class="auth-switch">
                            <p class="switch-text">Đã có tài khoản?</p>
                            <a href="<?php echo e(route('user.cus_login')); ?>" class="switch-link">
                                Đăng nhập ngay
                                <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>

                        <!-- Back to Home -->
                        <div class="back-to-home">
                            <a href="<?php echo e(route('home.index')); ?>" class="home-link">
                                <i class="fas fa-home me-2"></i>
                                Quay lại trang chủ
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom CSS -->
    <style>
    /* ===== JAPANESE AUTH STYLES (Same as login with modifications) ===== */
    .auth-body-japanese {
        font-family: var(--font-primary);
        background: linear-gradient(135deg, var(--light-gray) 0%, var(--white) 50%, var(--light-gray) 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-md);
    }

    .auth-container-japanese {
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
    }

    .auth-wrapper {
        background: var(--white);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
        display: grid;
        grid-template-columns: 1fr 1fr;
        min-height: 700px;
    }

    /* Left Side - Branding */
    .auth-branding {
        background: linear-gradient(135deg, var(--success-green) 0%, var(--primary-blue) 100%);
        color: var(--white);
        padding: var(--spacing-3xl);
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }

    .auth-branding::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        animation: float 20s infinite linear;
    }

    @keyframes float {
        0% { transform: translateX(0) translateY(0); }
        100% { transform: translateX(-50px) translateY(-50px); }
    }

    .branding-content {
        position: relative;
        z-index: 2;
    }

    .logo-section {
        margin-bottom: var(--spacing-2xl);
    }

    .brand-logo {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        text-decoration: none;
        color: var(--white);
    }

    .logo-img {
        height: 60px;
        width: auto;
        filter: brightness(0) invert(1);
    }

    .brand-text {
        font-size: var(--font-size-2xl);
        font-weight: 700;
    }

    .welcome-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        margin-bottom: var(--spacing-md);
        line-height: 1.2;
    }

    .welcome-subtitle {
        font-size: var(--font-size-lg);
        opacity: 0.9;
        line-height: 1.6;
        margin-bottom: var(--spacing-2xl);
    }

    .branding-features {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .feature-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        font-size: var(--font-size-base);
        opacity: 0.9;
    }

    .feature-item i {
        font-size: var(--font-size-lg);
        width: 24px;
    }

    /* Right Side - Form */
    .auth-form-section {
        padding: var(--spacing-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        overflow-y: auto;
    }

    .form-container {
        width: 100%;
        max-width: 400px;
    }

    .form-header {
        text-align: center;
        margin-bottom: var(--spacing-xl);
    }

    .form-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--neutral-black);
        margin-bottom: var(--spacing-sm);
    }

    .form-subtitle {
        color: var(--neutral-gray);
        font-size: var(--font-size-base);
        margin: 0;
    }

    .auth-form {
        width: 100%;
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }

    .form-label {
        display: flex;
        align-items: center;
        font-weight: 600;
        color: var(--neutral-black);
        margin-bottom: var(--spacing-sm);
    }

    .form-control {
        height: 50px;
        border: 2px solid var(--light-gray);
        border-radius: var(--radius-md);
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-base);
        transition: all var(--transition-fast);
        background: var(--white);
    }

    .form-control:focus {
        border-color: var(--primary-blue);
        box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
        outline: none;
    }

    .form-control.is-invalid {
        border-color: var(--primary-red);
    }

    .password-input-wrapper {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: var(--spacing-md);
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--neutral-gray);
        cursor: pointer;
        padding: var(--spacing-xs);
        transition: color var(--transition-fast);
    }

    .password-toggle:hover {
        color: var(--primary-blue);
    }

    .form-check {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-lg);
    }

    .form-check-input {
        width: 18px;
        height: 18px;
        border: 2px solid var(--neutral-gray);
        border-radius: var(--radius-sm);
        margin-top: 2px;
        flex-shrink: 0;
    }

    .form-check-input:checked {
        background-color: var(--success-green);
        border-color: var(--success-green);
    }

    .form-check-label {
        font-size: var(--font-size-sm);
        color: var(--neutral-gray);
        margin: 0;
        line-height: 1.4;
    }

    .terms-link {
        color: var(--primary-blue);
        text-decoration: none;
        font-weight: 500;
    }

    .terms-link:hover {
        color: var(--primary-red);
        text-decoration: underline;
    }

    .btn-register {
        width: 100%;
        height: 50px;
        font-size: var(--font-size-lg);
        font-weight: 600;
        margin-bottom: var(--spacing-xl);
        background: var(--success-green);
        border-color: var(--success-green);
    }

    .btn-register:hover {
        background: #2E7D32;
        border-color: #2E7D32;
    }

    .auth-switch {
        text-align: center;
        padding: var(--spacing-lg) 0;
        border-top: 1px solid var(--light-gray);
        margin-bottom: var(--spacing-lg);
    }

    .switch-text {
        color: var(--neutral-gray);
        margin-bottom: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }

    .switch-link {
        color: var(--primary-blue);
        text-decoration: none;
        font-weight: 600;
        font-size: var(--font-size-base);
        transition: color var(--transition-fast);
    }

    .switch-link:hover {
        color: var(--primary-red);
    }

    .back-to-home {
        text-align: center;
    }

    .home-link {
        color: var(--neutral-gray);
        text-decoration: none;
        font-size: var(--font-size-sm);
        transition: color var(--transition-fast);
    }

    .home-link:hover {
        color: var(--primary-blue);
    }

    /* Responsive Design */
    @media (max-width: 992px) {
        .auth-wrapper {
            grid-template-columns: 1fr;
        }

        .auth-branding {
            padding: var(--spacing-2xl);
            text-align: center;
        }

        .welcome-title {
            font-size: var(--font-size-2xl);
        }

        .branding-features {
            flex-direction: row;
            justify-content: center;
            flex-wrap: wrap;
        }
    }

    @media (max-width: 768px) {
        .auth-body-japanese {
            padding: var(--spacing-sm);
        }

        .auth-branding,
        .auth-form-section {
            padding: var(--spacing-xl);
        }

        .form-group {
            margin-bottom: var(--spacing-md);
        }

        .branding-features {
            flex-direction: column;
            align-items: center;
        }

        .auth-wrapper {
            min-height: auto;
        }
    }
    </style>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Toggle password visibility
        function togglePassword(inputId, iconId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(iconId);

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Form validation and submission
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('.btn-register');
            const originalText = submitBtn.innerHTML;
            const termsCheckbox = document.getElementById('terms');

            // Check if terms are accepted
            if (!termsCheckbox.checked) {
                e.preventDefault();
                alert('Vui lòng đồng ý với điều khoản sử dụng và chính sách bảo mật.');
                return;
            }

            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang tạo tài khoản...';
            submitBtn.disabled = true;

            // Reset button after 5 seconds if form doesn't submit
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            }, 5000);
        });

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });

        // Password strength indicator
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthIndicator = document.getElementById('passwordStrength');

            if (password.length === 0) {
                if (strengthIndicator) strengthIndicator.remove();
                return;
            }

            let strength = 0;
            let strengthText = '';
            let strengthColor = '';

            // Check password criteria
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            switch (strength) {
                case 0:
                case 1:
                    strengthText = 'Rất yếu';
                    strengthColor = '#dc3545';
                    break;
                case 2:
                    strengthText = 'Yếu';
                    strengthColor = '#fd7e14';
                    break;
                case 3:
                    strengthText = 'Trung bình';
                    strengthColor = '#ffc107';
                    break;
                case 4:
                    strengthText = 'Mạnh';
                    strengthColor = '#20c997';
                    break;
                case 5:
                    strengthText = 'Rất mạnh';
                    strengthColor = '#198754';
                    break;
            }

            // Create or update strength indicator
            let indicator = document.getElementById('passwordStrength');
            if (!indicator) {
                indicator = document.createElement('div');
                indicator.id = 'passwordStrength';
                indicator.style.fontSize = '12px';
                indicator.style.marginTop = '5px';
                this.parentElement.parentElement.appendChild(indicator);
            }

            indicator.innerHTML = `Độ mạnh mật khẩu: <span style="color: ${strengthColor}; font-weight: 600;">${strengthText}</span>`;
        });

        // Add floating animation to form elements
        const formInputs = document.querySelectorAll('.form-control');
        formInputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-2px)';
                this.parentElement.style.transition = 'transform 0.3s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'translateY(0)';
            });
        });

        // Phone number formatting
        document.getElementById('phone').addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length > 10) {
                value = value.substring(0, 10);
            }
            this.value = value;
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/cus_register.blade.php ENDPATH**/ ?>