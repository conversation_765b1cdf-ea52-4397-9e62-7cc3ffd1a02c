<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('new_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->text('short_description')->nullable();
            
            // Category hierarchy
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->integer('sort_order')->default(0);
            $table->integer('level')->default(0); // Category depth level
            
            // Images and media
            $table->string('image')->nullable();
            $table->string('banner_image')->nullable();
            $table->string('icon')->nullable();
            
            // Display settings
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->boolean('show_in_menu')->default(true);
            $table->string('display_mode')->default('products'); // products, subcategories, both
            
            // SEO fields
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->string('canonical_url')->nullable();
            $table->json('og_tags')->nullable(); // Open Graph tags
            
            // Product display settings
            $table->integer('products_per_page')->default(12);
            $table->string('default_sort')->default('created_at'); // created_at, name, price, popularity
            $table->json('available_filters')->nullable(); // Available filter options
            
            // Commission settings (for marketplace)
            $table->decimal('commission_rate', 5, 2)->nullable();
            
            $table->timestamps();
            
            // Foreign key constraint
            $table->foreign('parent_id')->references('id')->on('new_categories')->onDelete('cascade');
            
            // Indexes
            $table->index(['parent_id', 'is_active']);
            $table->index(['slug', 'is_active']);
            $table->index(['is_featured', 'is_active']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('new_categories');
    }
};
