<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class NewsletterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Newsletter subscribers
        DB::table('newsletter_subscribers')->insert([
            [
                'email' => '<EMAIL>',
                'first_name' => 'Nguyễn',
                'last_name' => 'Văn A',
                'status' => 'subscribed',
                'subscribed_at' => Carbon::now()->subDays(30),
                'subscription_source' => 'footer',
                'preferences' => json_encode([
                    'new_products' => true,
                    'promotions' => true,
                    'fashion_tips' => true,
                    'weekly_newsletter' => true
                ]),
                'unsubscribe_token' => bin2hex(random_bytes(32)),
                'created_at' => Carbon::now()->subDays(30),
                'updated_at' => Carbon::now()->subDays(30),
            ],
            [
                'email' => '<EMAIL>',
                'first_name' => 'Trần',
                'last_name' => 'Thị B',
                'status' => 'subscribed',
                'subscribed_at' => Carbon::now()->subDays(25),
                'subscription_source' => 'popup',
                'preferences' => json_encode([
                    'new_products' => true,
                    'promotions' => true,
                    'fashion_tips' => false,
                    'weekly_newsletter' => false
                ]),
                'unsubscribe_token' => bin2hex(random_bytes(32)),
                'created_at' => Carbon::now()->subDays(25),
                'updated_at' => Carbon::now()->subDays(25),
            ],
            [
                'email' => '<EMAIL>',
                'first_name' => 'Lê',
                'last_name' => 'Thị C',
                'status' => 'subscribed',
                'subscribed_at' => Carbon::now()->subDays(20),
                'subscription_source' => 'checkout',
                'preferences' => json_encode([
                    'new_products' => false,
                    'promotions' => true,
                    'fashion_tips' => true,
                    'weekly_newsletter' => true
                ]),
                'unsubscribe_token' => bin2hex(random_bytes(32)),
                'created_at' => Carbon::now()->subDays(20),
                'updated_at' => Carbon::now()->subDays(20),
            ],
            [
                'email' => '<EMAIL>',
                'first_name' => 'Phạm',
                'last_name' => 'Văn D',
                'status' => 'unsubscribed',
                'subscribed_at' => Carbon::now()->subDays(60),
                'unsubscribed_at' => Carbon::now()->subDays(10),
                'subscription_source' => 'footer',
                'preferences' => null,
                'unsubscribe_token' => bin2hex(random_bytes(32)),
                'created_at' => Carbon::now()->subDays(60),
                'updated_at' => Carbon::now()->subDays(10),
            ]
        ]);

        // Newsletter campaigns
        DB::table('newsletter_campaigns')->insert([
            [
                'name' => 'Chào mừng bộ sưu tập mùa hè 2024',
                'subject' => '🌞 Khám phá bộ sưu tập mùa hè 2024 - Giảm giá đến 50%!',
                'content' => '<html>
<head>
    <title>Bộ sưu tập mùa hè 2024</title>
</head>
<body>
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <header style="background-color: #ff6b6b; color: white; padding: 20px; text-align: center;">
            <h1>Shop Thời Trang</h1>
            <h2>Bộ sưu tập mùa hè 2024</h2>
        </header>
        
        <main style="padding: 20px;">
            <p>Chào bạn!</p>
            <p>Mùa hè đã đến và chúng tôi rất vui mừng giới thiệu bộ sưu tập mới nhất với những xu hướng thời trang hot nhất năm 2024.</p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="#" style="background-color: #4ecdc4; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                    Khám phá ngay
                </a>
            </div>
            
            <h3>Ưu đãi đặc biệt:</h3>
            <ul>
                <li>Giảm giá đến 50% cho toàn bộ bộ sưu tập</li>
                <li>Miễn phí vận chuyển cho đơn từ 500k</li>
                <li>Tặng voucher 100k cho lần mua tiếp theo</li>
            </ul>
        </main>
        
        <footer style="background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px;">
            <p>Shop Thời Trang - 123 Nguyễn Huệ, Quận 1, TP.HCM</p>
            <p><a href="#">Hủy đăng ký</a></p>
        </footer>
    </div>
</body>
</html>',
                'template' => 'promotional',
                'status' => 'sent',
                'scheduled_at' => Carbon::now()->subDays(7),
                'sent_at' => Carbon::now()->subDays(7),
                'total_recipients' => 3,
                'sent_count' => 3,
                'delivered_count' => 3,
                'opened_count' => 2,
                'clicked_count' => 1,
                'bounced_count' => 0,
                'unsubscribed_count' => 0,
                'created_at' => Carbon::now()->subDays(10),
                'updated_at' => Carbon::now()->subDays(7),
            ],
            [
                'name' => 'Newsletter tuần này',
                'subject' => '📰 Newsletter tuần này - Xu hướng thời trang & Tips phối đồ',
                'content' => '<html>
<head>
    <title>Newsletter tuần này</title>
</head>
<body>
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <header style="background-color: #6c5ce7; color: white; padding: 20px; text-align: center;">
            <h1>Shop Thời Trang</h1>
            <h2>Newsletter tuần này</h2>
        </header>
        
        <main style="padding: 20px;">
            <h3>🔥 Xu hướng hot nhất tuần này</h3>
            <p>Phong cách Y2K đang quay trở lại mạnh mẽ với những chi tiết metallic và màu sắc neon bắt mắt.</p>
            
            <h3>💡 Tips phối đồ</h3>
            <p>Cách phối đồ công sở thanh lịch: Kết hợp áo blazer với quần tây và giày cao gót để tạo nên vẻ ngoài chuyên nghiệp.</p>
            
            <h3>🛍️ Sản phẩm nổi bật</h3>
            <p>Đầm maxi hoa nhí - Perfect cho những buổi dạo phố cuối tuần.</p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="#" style="background-color: #fd79a8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                    Xem thêm
                </a>
            </div>
        </main>
        
        <footer style="background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px;">
            <p>Shop Thời Trang - 123 Nguyễn Huệ, Quận 1, TP.HCM</p>
            <p><a href="#">Hủy đăng ký</a></p>
        </footer>
    </div>
</body>
</html>',
                'template' => 'newsletter',
                'status' => 'scheduled',
                'scheduled_at' => Carbon::now()->addDays(3),
                'total_recipients' => 3,
                'sent_count' => 0,
                'delivered_count' => 0,
                'opened_count' => 0,
                'clicked_count' => 0,
                'bounced_count' => 0,
                'unsubscribed_count' => 0,
                'created_at' => Carbon::now()->subDays(2),
                'updated_at' => Carbon::now()->subDays(2),
            ],
            [
                'name' => 'Flash Sale cuối tuần',
                'subject' => '⚡ Flash Sale 70% - Chỉ còn 24h!',
                'content' => '<html>
<head>
    <title>Flash Sale cuối tuần</title>
</head>
<body>
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <header style="background-color: #e17055; color: white; padding: 20px; text-align: center;">
            <h1>⚡ FLASH SALE ⚡</h1>
            <h2>Giảm giá đến 70%</h2>
        </header>
        
        <main style="padding: 20px;">
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; text-align: center;">
                <h3 style="color: #856404; margin: 0;">⏰ Chỉ còn 24 giờ!</h3>
                <p style="color: #856404; margin: 5px 0;">Nhanh tay kẻo lỡ cơ hội vàng này!</p>
            </div>
            
            <p>Đây là cơ hội hiếm có để sở hữu những sản phẩm yêu thích với giá cực ưu đãi!</p>
            
            <h3>🎯 Sản phẩm hot sale:</h3>
            <ul>
                <li>Đầm nữ - Giảm 70%</li>
                <li>Áo thun nam - Giảm 60%</li>
                <li>Quần jean - Giảm 50%</li>
                <li>Phụ kiện - Giảm 40%</li>
            </ul>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="#" style="background-color: #d63031; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 18px;">
                    MUA NGAY
                </a>
            </div>
        </main>
        
        <footer style="background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px;">
            <p>Shop Thời Trang - 123 Nguyễn Huệ, Quận 1, TP.HCM</p>
            <p><a href="#">Hủy đăng ký</a></p>
        </footer>
    </div>
</body>
</html>',
                'template' => 'flash_sale',
                'status' => 'draft',
                'total_recipients' => 0,
                'sent_count' => 0,
                'delivered_count' => 0,
                'opened_count' => 0,
                'clicked_count' => 0,
                'bounced_count' => 0,
                'unsubscribed_count' => 0,
                'created_at' => Carbon::now()->subDays(1),
                'updated_at' => Carbon::now()->subDays(1),
            ]
        ]);
    }
}
